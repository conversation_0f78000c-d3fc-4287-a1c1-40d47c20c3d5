using System.Text.Json;
using Microsoft.Extensions.Logging;
using Winmug.Core.Authentication;
using Winmug.Core.Models;

namespace Winmug.Core.Services;

/// <summary>
/// Improved SmugMug API client following best practices from API documentation
/// </summary>
public class ImprovedSmugMugApiClient : ISmugMugApiClient
{
    private readonly HttpClient _httpClient;
    private readonly ISmugMugAuthenticationService _authService;
    private readonly ILogger<ImprovedSmugMugApiClient> _logger;
    private readonly JsonSerializerOptions _jsonOptions;

    private const string BaseApiUrl = "https://api.smugmug.com/api/v2";

    // URI Cache to store all URIs from the authuser response for efficient navigation
    private SmugMugUris? _cachedUserUris;
    private string? _cachedRootNodeId;
    private DateTime? _uriCacheTime;

    public ImprovedSmugMugApiClient(
        HttpClient httpClient,
        ISmugMugAuthenticationService authService,
        ILogger<ImprovedSmugMugApiClient> logger)
    {
        _httpClient = httpClient;
        _authService = authService;
        _logger = logger;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };
    }

    /// <summary>
    /// Cache URIs from the authuser response for efficient navigation throughout the session
    /// This implements the pattern described in SmugMugAPI.md to cache all URIs from /api/v2!authuser
    /// </summary>
    private void CacheUserUris(SmugMugUser user)
    {
        if (user.Uris != null)
        {
            _cachedUserUris = user.Uris;
            _uriCacheTime = DateTime.UtcNow;

            // Extract and cache the root node ID from the Node URI
            if (user.Uris.Node?.Uri != null)
            {
                var nodeUri = user.Uris.Node.Uri;
                _cachedRootNodeId = nodeUri.Split('/').LastOrDefault();
                _logger.LogInformation("🗂️ Cached user URIs and root node ID: {NodeId}", _cachedRootNodeId);
                _logger.LogDebug("📋 Available cached URIs: Node, Folder, UserAlbums, UserRecentImages, and {UriCount} others",
                    CountAvailableUris(user.Uris));
            }
        }
    }

    /// <summary>
    /// Count available URIs for logging purposes
    /// </summary>
    private int CountAvailableUris(SmugMugUris uris)
    {
        var count = 0;
        var properties = typeof(SmugMugUris).GetProperties();
        foreach (var prop in properties)
        {
            if (prop.GetValue(uris) is SmugMugUriInfo uriInfo && !string.IsNullOrEmpty(uriInfo.Uri))
            {
                count++;
            }
        }
        return count;
    }

    /// <summary>
    /// Clear the URI cache (call when user logs out or session ends)
    /// </summary>
    public void ClearUriCache()
    {
        _cachedUserUris = null;
        _cachedRootNodeId = null;
        _uriCacheTime = null;
        _logger.LogInformation("🗑️ Cleared URI cache");
    }

    /// <summary>
    /// Get a cached URI for efficient navigation
    /// </summary>
    private string? GetCachedUri(Func<SmugMugUris, SmugMugUriInfo?> uriSelector)
    {
        if (_cachedUserUris == null)
        {
            _logger.LogDebug("No cached URIs available - need to call GetAuthenticatedUserAsync first");
            return null;
        }

        var uriInfo = uriSelector(_cachedUserUris);
        return uriInfo?.Uri;
    }

    /// <summary>
    /// Get the cached root node ID for efficient access
    /// </summary>
    private string? GetCachedRootNodeId()
    {
        if (string.IsNullOrEmpty(_cachedRootNodeId))
        {
            _logger.LogDebug("No cached root node ID available - need to call GetAuthenticatedUserAsync first");
            return null;
        }

        return _cachedRootNodeId;
    }

    /// <summary>
    /// Get all user albums using cached URI for efficient access
    /// </summary>
    public async Task<List<SmugMugAlbum>> GetAllUserAlbumsAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting all user albums using cached URI...");

        // Try to use cached UserAlbums URI first
        var userAlbumsUri = GetCachedUri(uris => uris.UserAlbums);

        if (userAlbumsUri != null)
        {
            _logger.LogDebug("Using cached UserAlbums URI: {Uri}", userAlbumsUri);
            var albums = new List<SmugMugAlbum>();

            await foreach (var album in GetPagedResultsAsync<SmugMugAlbum>(userAlbumsUri, cancellationToken))
            {
                albums.Add(album);
            }

            _logger.LogInformation("Found {AlbumCount} albums using cached URI", albums.Count);
            return albums;
        }

        // Fallback: get user and use UserAlbums URI
        _logger.LogDebug("No cached URI available, getting user albums via authuser");
        var user = await GetAuthenticatedUserAsync(cancellationToken);

        if (user.Uris?.UserAlbums?.Uri != null)
        {
            var albums = new List<SmugMugAlbum>();

            await foreach (var album in GetPagedResultsAsync<SmugMugAlbum>(user.Uris.UserAlbums.Uri, cancellationToken))
            {
                albums.Add(album);
            }

            _logger.LogInformation("Found {AlbumCount} albums using fallback method", albums.Count);
            return albums;
        }

        throw new InvalidOperationException("Unable to access user albums - no UserAlbums URI available");
    }

    /// <summary>
    /// Gets the authenticated user information
    /// </summary>
    public async Task<SmugMugUser> GetAuthenticatedUserAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting authenticated user information...");

        // Use the correct endpoint without _expand=Node since we get Node URI directly in response
        // The _expand=Node parameter was causing issues and is not needed
        var url = $"{BaseApiUrl}!authuser";
        _logger.LogDebug("Making authenticated request to: {Url}", url);

        // First, let's get the raw response to see exactly what we're receiving
        var request = _authService.CreateAuthenticatedRequest(HttpMethod.Get, url);
        request.Headers.Add("Accept", "application/json");

        _logger.LogInformation("Making authenticated request to: {Url}", url);
        _logger.LogDebug("Request headers: {Headers}", string.Join(", ", request.Headers.Select(h => $"{h.Key}: {string.Join(", ", h.Value)}")));

        var httpResponse = await _httpClient.SendAsync(request, cancellationToken);
        var rawContent = await httpResponse.Content.ReadAsStringAsync(cancellationToken);

        _logger.LogInformation("Raw API Response Status: {StatusCode} {ReasonPhrase}", httpResponse.StatusCode, httpResponse.ReasonPhrase);
        _logger.LogInformation("Raw API Response Content Length: {Length}", rawContent.Length);
        _logger.LogDebug("Raw API Response Content: {Content}", rawContent.Length > 1000 ? rawContent.Substring(0, 1000) + "..." : rawContent);

        if (!httpResponse.IsSuccessStatusCode)
        {
            _logger.LogError("API request failed with status {StatusCode}: {Content}", httpResponse.StatusCode, rawContent);
            throw new InvalidOperationException($"Failed to get authenticated user information: {httpResponse.StatusCode} - {rawContent}");
        }

        // LOG REQUEST DETAILS FOR DEBUGGING
        _logger.LogInformation("🔍 MAKING AUTHUSER REQUEST:");
        _logger.LogInformation("   URL: {Url}", url);
        _logger.LogInformation("   Method: GET");
        _logger.LogInformation("   Expected Response Type: SmugMugApiResponse<SmugMugAuthUserResponse>");

        var response = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugAuthUserResponse>>(HttpMethod.Get, url, cancellationToken);

        // LOG COMPLETE RAW RESPONSE FOR DEBUGGING
        _logger.LogInformation("=== COMPLETE AUTHUSER RESPONSE DEBUG ===");
        try
        {
            var responseJson = System.Text.Json.JsonSerializer.Serialize(response, new System.Text.Json.JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = null // Keep original property names
            });
            _logger.LogInformation("📋 FULL AUTHUSER RESPONSE JSON:");
            _logger.LogInformation("{ResponseJson}", responseJson);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "❌ Failed to serialize response for logging");
        }
        _logger.LogInformation("=== END AUTHUSER RESPONSE DEBUG ===");

        if (response?.Response?.User == null)
        {
            _logger.LogError("❌ CRITICAL: Failed to get authenticated user information");
            _logger.LogError("   Response is null: {ResponseNull}", response == null);
            _logger.LogError("   Response.Response is null: {ResponseResponseNull}", response?.Response == null);
            _logger.LogError("   Response.Response.User is null: {UserNull}", response?.Response?.User == null);
            throw new InvalidOperationException("Failed to get authenticated user information");
        }

        var user = response.Response.User;

        // Enhanced logging to capture comprehensive user information
        _logger.LogInformation("📋 COMPREHENSIVE USER INFORMATION:");
        _logger.LogInformation("  Name: '{Name}'", user.Name ?? "NULL");
        _logger.LogInformation("  NickName: '{NickName}'", user.NickName ?? "NULL");
        _logger.LogInformation("  FirstName: '{FirstName}'", user.FirstName ?? "NULL");
        _logger.LogInformation("  LastName: '{LastName}'", user.LastName ?? "NULL");
        _logger.LogInformation("  Email: '{Email}'", user.Email ?? "NULL");
        _logger.LogInformation("  Plan: '{Plan}'", user.Plan ?? "NULL");
        _logger.LogInformation("  AccountStatus: '{AccountStatus}'", user.AccountStatus ?? "NULL");
        _logger.LogInformation("  ImageCount: {ImageCount}", user.ImageCount ?? 0);
        _logger.LogInformation("  WebUri: '{WebUri}'", user.WebUri ?? "NULL");
        _logger.LogInformation("  Uri: '{Uri}'", user.Uri ?? "NULL");
        _logger.LogInformation("  Has Uris: {HasUris}", user.Uris != null);

        // Log Node ID extraction from URI
        if (!string.IsNullOrEmpty(user.NodeId))
        {
            _logger.LogInformation("  ✅ NODE ID EXTRACTED: {NodeId}", user.NodeId);
        }
        else
        {
            _logger.LogWarning("  ❌ NO NODE ID FOUND - This indicates limited access permissions!");
        }

        // Log all available URIs from the response
        if (user.Uris != null)
        {
            _logger.LogInformation("📋 AVAILABLE URIs:");
            if (user.Uris.Node?.Uri != null)
                _logger.LogInformation("  ✅ Node URI: '{NodeUri}' -> Node ID: {NodeId}", user.Uris.Node.Uri, user.NodeId);
            if (user.Uris.Folder?.Uri != null)
                _logger.LogInformation("  ✅ Folder URI: '{FolderUri}'", user.Uris.Folder.Uri);
            if (user.Uris.UserAlbums?.Uri != null)
                _logger.LogInformation("  ✅ UserAlbums URI: '{UserAlbumsUri}'", user.Uris.UserAlbums.Uri);
            if (user.Uris.UserRecentImages?.Uri != null)
                _logger.LogInformation("  ✅ UserRecentImages URI: '{UserRecentImagesUri}'", user.Uris.UserRecentImages.Uri);
            if (user.Uris.UserFeaturedAlbums?.Uri != null)
                _logger.LogInformation("  ✅ UserFeaturedAlbums URI: '{UserFeaturedAlbumsUri}'", user.Uris.UserFeaturedAlbums.Uri);
        }
        else
        {
            _logger.LogWarning("  ❌ NO URIs FOUND - This indicates limited access permissions!");
        }

        _logger.LogInformation("Successfully retrieved user: {UserName} ({NickName})",
            user.EffectiveName, user.EffectiveNickName);

        // CRITICAL: Cache all URIs from the authuser response for efficient navigation
        CacheUserUris(user);

        return user;
    }

    /// <summary>
    /// Gets the user's root node using cached Node ID for efficient access
    /// </summary>
    public async Task<SmugMugNode> GetUserRootNodeAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting user root node using cached Node ID...");

        // Step 1: Try to use cached root node ID first
        var cachedNodeId = GetCachedRootNodeId();
        if (!string.IsNullOrEmpty(cachedNodeId))
        {
            _logger.LogInformation("✅ Using cached root node ID: {NodeId}", cachedNodeId);
            try
            {
                var rootNode = await GetNodeAsync(cachedNodeId, cancellationToken);
                _logger.LogInformation("✅ Successfully retrieved root node from cache: {NodeName} (ID: {NodeId}, Type: {Type})",
                    rootNode.Name, rootNode.NodeId, rootNode.Type);
                return rootNode;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to get node using cached Node ID {NodeId}, refreshing cache", cachedNodeId);
            }
        }

        // Step 2: Get authenticated user to retrieve and cache Node ID
        var user = await GetAuthenticatedUserAsync(cancellationToken);
        _logger.LogDebug("User data retrieved: {UserName} ({NickName}) with Node ID: {NodeId}",
            user.Name, user.NickName, user.NodeId);

        // Step 3: Use the Node ID directly from the authuser response
        if (!string.IsNullOrEmpty(user.NodeId))
        {
            _logger.LogInformation("✅ Using Node ID from authuser response: {NodeId}", user.NodeId);
            try
            {
                // Get the node details using the Node ID
                var rootNode = await GetNodeAsync(user.NodeId, cancellationToken);

                _logger.LogInformation("✅ Successfully retrieved root node: {NodeName} (ID: {NodeId}, Type: {Type})",
                    rootNode.Name, rootNode.NodeId, rootNode.Type);

                return rootNode;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to get node using Node ID {NodeId}, trying fallback methods", user.NodeId);
            }
        }

        // Step 3: Fallback to Node URI approach if Node ID didn't work
        if (user.Uris?.Node?.Uri != null)
        {
            _logger.LogDebug("Fallback: Using Node URI from user response: {NodeUri}", user.Uris.Node.Uri);
            try
            {
                var response = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugNode>>(HttpMethod.Get, user.Uris.Node.Uri, cancellationToken);

                if (response?.Response != null)
                {
                    _logger.LogInformation("✅ Successfully retrieved root node via Node URI: {NodeName} (ID: {NodeId}, Type: {Type})",
                        response.Response.Name, response.Response.NodeId, response.Response.Type);

                    return response.Response;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Node URI approach failed, trying fallback methods");
            }
        }
        else
        {
            _logger.LogWarning("No Node URI found in user response - this indicates limited OAuth permissions");
        }

        // Step 4: Try fallback approaches for limited access scenarios
        return await TryFallbackRootNodeApproaches(user, cancellationToken);
    }

    /// <summary>
    /// Try various fallback approaches to get the root node when primary method fails
    /// </summary>
    private async Task<SmugMugNode> TryFallbackRootNodeApproaches(SmugMugUser user, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Trying fallback approaches for root node access...");

        // Approach 1: Try !siteuser endpoint (alternative authenticated user endpoint)
        try
        {
            _logger.LogDebug("Fallback 1: Trying !siteuser endpoint");
            var siteUserUrl = $"{BaseApiUrl}!siteuser";

            // Try the nested structure first (like !authuser)
            try
            {
                var siteUserResponse = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugAuthUserResponse>>(HttpMethod.Get, siteUserUrl, cancellationToken);
                if (siteUserResponse?.Response?.User?.Uris?.Node?.Uri != null)
                {
                    var nodeResponse = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugNode>>(HttpMethod.Get, siteUserResponse.Response.User.Uris.Node.Uri, cancellationToken);
                    if (nodeResponse?.Response != null)
                    {
                        _logger.LogInformation("✓ Root node found via !siteuser (nested): {NodeName} (ID: {NodeId})",
                            nodeResponse.Response.Name, nodeResponse.Response.NodeId);
                        return nodeResponse.Response;
                    }
                }
            }
            catch
            {
                // Try direct structure if nested fails
                var siteUserResponse = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugUser>>(HttpMethod.Get, siteUserUrl, cancellationToken);
                if (siteUserResponse?.Response?.Uris?.Node?.Uri != null)
                {
                    var nodeResponse = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugNode>>(HttpMethod.Get, siteUserResponse.Response.Uris.Node.Uri, cancellationToken);
                    if (nodeResponse?.Response != null)
                    {
                        _logger.LogInformation("✓ Root node found via !siteuser (direct): {NodeName} (ID: {NodeId})",
                            nodeResponse.Response.Name, nodeResponse.Response.NodeId);
                        return nodeResponse.Response;
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Fallback 1 (!siteuser) failed");
        }

        // Approach 1b: Try direct authuser!node endpoint
        try
        {
            _logger.LogDebug("Fallback 1b: Trying !authuser!node");
            var nodeUrl = $"{BaseApiUrl}!authuser!node";
            var nodeResponse = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugNode>>(HttpMethod.Get, nodeUrl, cancellationToken);

            if (nodeResponse?.Response != null)
            {
                _logger.LogInformation("✓ Root node found via authuser!node: {NodeName} (ID: {NodeId})",
                    nodeResponse.Response.Name, nodeResponse.Response.NodeId);
                return nodeResponse.Response;
            }
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Fallback 1b failed");
        }

        // Approach 2: Try user-specific node endpoint
        try
        {
            _logger.LogDebug("Fallback 2: Trying /user/{nickname}!node", user.NickName);
            var nodeUrl = $"{BaseApiUrl}/user/{user.NickName}!node";
            var nodeResponse = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugNode>>(HttpMethod.Get, nodeUrl, cancellationToken);

            if (nodeResponse?.Response != null)
            {
                _logger.LogInformation("✓ Root node found via user!node: {NodeName} (ID: {NodeId})",
                    nodeResponse.Response.Name, nodeResponse.Response.NodeId);
                return nodeResponse.Response;
            }
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Fallback 2 failed");
        }

        // Approach 3: Try to get user with Full response level
        try
        {
            _logger.LogDebug("Fallback 3: Trying user with Full response level");
            var fullUserUrl = $"{BaseApiUrl}/user/{user.NickName}?_responseLevel=Full";
            var fullUserResponse = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugUser>>(HttpMethod.Get, fullUserUrl, cancellationToken);

            if (fullUserResponse?.Response?.Uris?.Node?.Uri != null)
            {
                var nodeResponse = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugNode>>(HttpMethod.Get, fullUserResponse.Response.Uris.Node.Uri, cancellationToken);
                if (nodeResponse?.Response != null)
                {
                    _logger.LogInformation("✓ Root node found via Full response level: {NodeName} (ID: {NodeId})",
                        nodeResponse.Response.Name, nodeResponse.Response.NodeId);
                    return nodeResponse.Response;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Fallback 3 failed");
        }

        // Approach 4: Create virtual root from albums (last resort)
        try
        {
            _logger.LogDebug("Fallback 4: Creating virtual root from accessible albums");
            var albumsUrl = $"{BaseApiUrl}/user/{user.NickName}!albums";
            var albumsResponse = await SendAuthenticatedRequestAsync<SmugMugApiResponse<List<SmugMugAlbum>>>(HttpMethod.Get, albumsUrl, cancellationToken);

            if (albumsResponse?.Response != null && albumsResponse.Response.Any())
            {
                _logger.LogInformation("✓ Creating virtual root node - found {AlbumCount} accessible albums", albumsResponse.Response.Count);

                // Create a virtual root node since we can access albums but not the folder structure
                var virtualRoot = new SmugMugNode
                {
                    NodeId = "virtual-root",
                    Name = $"{user.Name}'s Photos",
                    Type = "Folder",
                    Description = "Virtual root - albums accessible with current permissions",
                    DateAdded = DateTime.Now,
                    DateModified = DateTime.Now,
                    HasChildren = true
                };

                return virtualRoot;
            }
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Fallback 4 failed");
        }

        // All approaches failed - provide detailed error information
        var errorMessage = "❌ UNABLE TO ACCESS FOLDER STRUCTURE\n\n" +
                          "This indicates insufficient OAuth permissions. The application needs 'Full' access to retrieve your folder structure.\n\n" +
                          "To fix this:\n" +
                          "1. Click 'Logout' to clear current credentials\n" +
                          "2. Click 'Authenticate with SmugMug' to start fresh\n" +
                          "3. When the SmugMug authorization page opens, make sure to:\n" +
                          "   - Click 'Allow' or 'Authorize' when prompted\n" +
                          "   - Grant full access permissions\n" +
                          "   - Do not select 'Public access only'\n\n" +
                          "If you continue to have issues, your SmugMug account may have restrictions that prevent folder structure access.";

        _logger.LogError(errorMessage);
        throw new InvalidOperationException(errorMessage);
    }

    /// <summary>
    /// Gets a specific node by its ID
    /// </summary>
    public async Task<SmugMugNode> GetNodeAsync(string nodeId, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting node: {NodeId}", nodeId);
        
        var url = $"{BaseApiUrl}/node/{nodeId}";
        var response = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugNode>>(HttpMethod.Get, url, cancellationToken);
        
        if (response?.Response == null)
        {
            throw new InvalidOperationException($"Failed to get node: {nodeId}");
        }

        return response.Response;
    }

    /// <summary>
    /// Gets the child nodes of a specific node using the correct API approach
    /// Uses the Node ID directly from the authuser response without _expand parameter
    /// </summary>
    public async Task<List<SmugMugNode>> GetChildNodesAsync(string nodeId, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting child nodes for: {NodeId} using direct Node API approach", nodeId);

        // CRITICAL: Check if nodeId is null or empty before constructing URL
        if (string.IsNullOrEmpty(nodeId))
        {
            _logger.LogError("❌ CRITICAL ERROR: Node ID is null or empty in GetChildNodesAsync!");
            _logger.LogError("   This indicates that the user's Node ID was not extracted properly from the authuser response");
            _logger.LogError("   This usually means insufficient OAuth permissions or authentication issues");
            throw new InvalidOperationException("Cannot get child nodes: Node ID is null or empty. This indicates insufficient OAuth permissions. Please re-authenticate with full access.");
        }

        // First, get the node details to access the ChildNodes URI from the response
        var nodeInfo = await GetNodeAsync(nodeId, cancellationToken);

        // Check if the node has a ChildNodes URI in its response
        if (nodeInfo.Uris?.ChildNodes?.Uri != null)
        {
            _logger.LogDebug("Using ChildNodes URI from node response: {Uri}", nodeInfo.Uris.ChildNodes.Uri);
            var nodes = new List<SmugMugNode>();

            await foreach (var node in GetPagedResultsAsync<SmugMugNode>(nodeInfo.Uris.ChildNodes.Uri, cancellationToken))
            {
                nodes.Add(node);
            }

            _logger.LogDebug("Found {Count} child nodes for: {NodeId} using URI navigation", nodes.Count, nodeId);
            return nodes;
        }
        else
        {
            // Fallback to constructed URL if no ChildNodes URI is available
            var url = $"{BaseApiUrl}/node/{nodeId}!children";
            _logger.LogDebug("No ChildNodes URI found, using constructed URL: {Url}", url);

            var nodes = new List<SmugMugNode>();

            await foreach (var node in GetPagedResultsAsync<SmugMugNode>(url, cancellationToken))
            {
                nodes.Add(node);
            }

            _logger.LogDebug("Found {Count} child nodes for: {NodeId} using constructed URL", nodes.Count, nodeId);
            return nodes;
        }
    }

    /// <summary>
    /// Gets all child nodes recursively for a given node
    /// </summary>
    public async Task<List<SmugMugNode>> GetAllChildNodesRecursiveAsync(string nodeId, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting all child nodes recursively for: {NodeId}", nodeId);
        
        var allNodes = new List<SmugMugNode>();
        var nodesToProcess = new Queue<string>();
        nodesToProcess.Enqueue(nodeId);

        while (nodesToProcess.Count > 0)
        {
            var currentNodeId = nodesToProcess.Dequeue();
            var childNodes = await GetChildNodesAsync(currentNodeId, cancellationToken);
            
            foreach (var childNode in childNodes)
            {
                allNodes.Add(childNode);
                
                // If it's a folder, add it to the queue for recursive processing
                if (childNode.IsFolder)
                {
                    nodesToProcess.Enqueue(childNode.NodeId);
                }
            }
        }

        _logger.LogDebug("Found {Count} total child nodes recursively for: {NodeId}", allNodes.Count, nodeId);
        return allNodes;
    }

    /// <summary>
    /// Get the complete folder structure with album counts and size estimates using cached URIs
    /// REWRITTEN: Optimized approach following SmugMugAPI.md best practices
    /// 1. Start with /api/v2!authuser to cache all URIs
    /// 2. Use cached Node URI to build folder hierarchy
    /// 3. Implement robust error handling and fallbacks
    /// </summary>
    public async Task<FolderNode> GetFolderStructureAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("🔍 STARTING OPTIMIZED FOLDER STRUCTURE RETRIEVAL...");
        _logger.LogInformation("Following SmugMugAPI.md best practices: Start with /api/v2!authuser, cache URIs, use Node hierarchy");

        try
        {
            // Step 1: Get authenticated user and cache all URIs from /api/v2!authuser
            _logger.LogInformation("📡 Step 1: Calling /api/v2!authuser to get all authenticated user URLs...");
            var user = await GetAuthenticatedUserAsync(cancellationToken);

            if (_cachedUserUris == null)
            {
                throw new InvalidOperationException("Failed to cache URIs from /api/v2!authuser response");
            }

            _logger.LogInformation("✅ Successfully cached URIs from /api/v2!authuser:");
            LogAvailableUris();

            // Step 2: Use cached Node URI to get root node and build folder structure
            var cachedNodeUri = GetCachedUri(uris => uris.Node);
            if (cachedNodeUri != null)
            {
                _logger.LogInformation("🌳 Step 2: Using cached Node URI to build folder structure: {NodeUri}", cachedNodeUri);

                // Extract node ID from the cached URI
                var nodeId = ExtractNodeIdFromUri(cachedNodeUri);
                if (!string.IsNullOrEmpty(nodeId))
                {
                    _logger.LogInformation("📂 Building folder structure from root node: {NodeId}", nodeId);
                    var folderStructure = await BuildOptimizedFolderStructureAsync(nodeId, user.NickName, cancellationToken);

                    _logger.LogInformation("✅ FOLDER STRUCTURE COMPLETE!");
                    _logger.LogInformation("📊 Results: {AlbumCount} albums, {ImageCount} images, {Size} estimated size",
                        folderStructure.Albums.Count, folderStructure.TotalImageCount, FormatBytes(folderStructure.TotalEstimatedSizeBytes));

                    return folderStructure;
                }
            }

            // Step 3: Fallback to direct album retrieval if Node approach fails
            _logger.LogInformation("🔄 Step 3: Fallback - Creating virtual root with all user albums...");
            var virtualRoot = await CreateVirtualRootWithAllAlbumsAsync(user.NickName, cancellationToken);

            _logger.LogInformation("✅ VIRTUAL ROOT COMPLETE!");
            _logger.LogInformation("📊 Results: {AlbumCount} albums, {ImageCount} images, {Size} estimated size",
                virtualRoot.Albums.Count, virtualRoot.TotalImageCount, FormatBytes(virtualRoot.TotalEstimatedSizeBytes));

            return virtualRoot;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Failed to retrieve folder structure");
            throw new InvalidOperationException($"Failed to retrieve folder structure: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// Log available cached URIs for debugging
    /// </summary>
    private void LogAvailableUris()
    {
        if (_cachedUserUris == null) return;

        var uriCount = 0;
        if (_cachedUserUris.Node?.Uri != null)
        {
            _logger.LogInformation("  ✅ Node URI: {NodeUri}", _cachedUserUris.Node.Uri);
            uriCount++;
        }
        if (_cachedUserUris.Folder?.Uri != null)
        {
            _logger.LogInformation("  ✅ Folder URI: {FolderUri}", _cachedUserUris.Folder.Uri);
            uriCount++;
        }
        if (_cachedUserUris.UserAlbums?.Uri != null)
        {
            _logger.LogInformation("  ✅ UserAlbums URI: {UserAlbumsUri}", _cachedUserUris.UserAlbums.Uri);
            uriCount++;
        }
        if (_cachedUserUris.UserRecentImages?.Uri != null)
        {
            _logger.LogInformation("  ✅ UserRecentImages URI: {UserRecentImagesUri}", _cachedUserUris.UserRecentImages.Uri);
            uriCount++;
        }

        _logger.LogInformation("📋 Total cached URIs: {UriCount}", uriCount);
    }

    /// <summary>
    /// Extract node ID from a Node URI (e.g., "/api/v2/node/ABC123" -> "ABC123")
    /// </summary>
    private string? ExtractNodeIdFromUri(string nodeUri)
    {
        if (string.IsNullOrEmpty(nodeUri)) return null;

        var parts = nodeUri.Split('/');
        return parts.Length > 0 ? parts.Last() : null;
    }

    /// <summary>
    /// Build optimized folder structure using cached Node URI and efficient traversal
    /// This is the primary method that implements SmugMugAPI.md best practices
    /// </summary>
    private async Task<FolderNode> BuildOptimizedFolderStructureAsync(string rootNodeId, string userNickname, CancellationToken cancellationToken)
    {
        _logger.LogInformation("🌳 Building optimized folder structure from root node: {NodeId}", rootNodeId);

        try
        {
            // Get the root node details
            var rootNode = await GetNodeAsync(rootNodeId, cancellationToken);
            _logger.LogInformation("✅ Root node retrieved: {NodeName} (Type: {Type}, HasChildren: {HasChildren})",
                rootNode.Name, rootNode.Type, rootNode.HasChildren);

            // Create the root folder node
            var folderStructure = new FolderNode
            {
                NodeId = rootNode.NodeId,
                Name = rootNode.Name ?? $"{userNickname}'s Photos",
                Description = rootNode.Description ?? "Root folder",
                Type = rootNode.Type,
                UrlName = rootNode.UrlName ?? "",
                FullPath = "",
                DateCreated = rootNode.DateAdded ?? DateTime.MinValue,
                DateModified = rootNode.DateModified ?? DateTime.MinValue,
                HasChildren = rootNode.HasChildren ?? false
            };

            // Recursively build the folder structure if the root has children
            if (rootNode.HasChildren == true)
            {
                _logger.LogInformation("📁 Root node has children, building recursive structure...");
                await BuildFolderStructureRecursivelyOptimizedAsync(folderStructure, rootNodeId, "", cancellationToken);
            }
            else
            {
                _logger.LogInformation("📄 Root node has no children, checking for direct albums...");
                // Even if HasChildren is false, try to get albums directly
                await TryAddDirectAlbumsToFolderAsync(folderStructure, rootNodeId, cancellationToken);
            }

            _logger.LogInformation("✅ Optimized folder structure complete: {AlbumCount} albums, {ImageCount} images",
                folderStructure.Albums.Count, folderStructure.TotalImageCount);

            return folderStructure;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Failed to build optimized folder structure from node {NodeId}", rootNodeId);
            throw;
        }
    }

    /// <summary>
    /// Recursively build folder structure with optimized traversal and error handling
    /// </summary>
    private async Task BuildFolderStructureRecursivelyOptimizedAsync(FolderNode parentFolder, string nodeId, string parentPath, CancellationToken cancellationToken)
    {
        _logger.LogDebug("🔍 Processing node: {NodeId} (path: {Path})", nodeId, parentPath);

        try
        {
            // Get child nodes using cached URIs when possible
            var childNodes = await GetChildNodesAsync(nodeId, cancellationToken);
            _logger.LogDebug("Found {ChildCount} child nodes for {NodeId}", childNodes.Count, nodeId);

            foreach (var childNode in childNodes)
            {
                var childPath = string.IsNullOrEmpty(parentPath) ? childNode.Name : $"{parentPath}/{childNode.Name}";

                if (childNode.IsAlbum)
                {
                    // Process album
                    _logger.LogDebug("📸 Processing album: {AlbumName} (Node: {NodeId})", childNode.Name, childNode.NodeId);
                    var albumInfo = await GetAlbumInfoAsync(childNode, childPath, cancellationToken);
                    parentFolder.Albums.Add(albumInfo);
                    parentFolder.TotalImageCount += albumInfo.ImageCount;
                    parentFolder.TotalEstimatedSizeBytes += albumInfo.EstimatedSizeBytes;
                }
                else if (childNode.IsFolder)
                {
                    // Process subfolder
                    _logger.LogDebug("📁 Processing subfolder: {FolderName} (Node: {NodeId})", childNode.Name, childNode.NodeId);

                    var subFolder = new FolderNode
                    {
                        NodeId = childNode.NodeId,
                        Name = childNode.Name,
                        Description = childNode.Description ?? "",
                        Type = childNode.Type,
                        UrlName = childNode.UrlName ?? "",
                        FullPath = childPath,
                        DateCreated = childNode.DateAdded ?? DateTime.MinValue,
                        DateModified = childNode.DateModified ?? DateTime.MinValue,
                        HasChildren = childNode.HasChildren ?? false
                    };

                    // Recursively process subfolder if it has children
                    if (childNode.HasChildren == true)
                    {
                        await BuildFolderStructureRecursivelyOptimizedAsync(subFolder, childNode.NodeId, childPath, cancellationToken);
                    }

                    parentFolder.Children.Add(subFolder);
                    parentFolder.TotalImageCount += subFolder.TotalImageCount;
                    parentFolder.TotalEstimatedSizeBytes += subFolder.TotalEstimatedSizeBytes;
                }
                else
                {
                    _logger.LogDebug("❓ Unknown node type: {Type} for {NodeName}", childNode.Type, childNode.Name);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Failed to process node {NodeId}, continuing with other nodes", nodeId);
            // Continue processing other nodes even if one fails
        }
    }

    /// <summary>
    /// Try to add albums directly to a folder node (fallback for nodes that report no children)
    /// </summary>
    private async Task TryAddDirectAlbumsToFolderAsync(FolderNode folderNode, string nodeId, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("🔍 Attempting to find direct albums for node: {NodeId}", nodeId);

            // Try to get albums using various approaches
            var albums = await GetAlbumsForNodeAsync(nodeId, cancellationToken);

            foreach (var album in albums)
            {
                var albumInfo = await GetAlbumInfoAsync(album, folderNode.FullPath, cancellationToken);
                folderNode.Albums.Add(albumInfo);
                folderNode.TotalImageCount += albumInfo.ImageCount;
                folderNode.TotalEstimatedSizeBytes += albumInfo.EstimatedSizeBytes;
            }

            if (albums.Count > 0)
            {
                _logger.LogInformation("✅ Found {AlbumCount} direct albums for node {NodeId}", albums.Count, nodeId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "No direct albums found for node {NodeId}", nodeId);
            // This is not an error - some nodes simply don't have albums
        }
    }

    /// <summary>
    /// Get albums for a specific node using various API approaches
    /// </summary>
    private async Task<List<SmugMugNode>> GetAlbumsForNodeAsync(string nodeId, CancellationToken cancellationToken)
    {
        var albums = new List<SmugMugNode>();

        try
        {
            // Try to get albums using the node's album endpoint
            var albumsUrl = $"{BaseApiUrl}/node/{nodeId}!albums";

            await foreach (var node in GetPagedResultsAsync<SmugMugNode>(albumsUrl, cancellationToken))
            {
                if (node.IsAlbum)
                {
                    albums.Add(node);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Failed to get albums for node {NodeId} using !albums endpoint", nodeId);
        }

        return albums;
    }

    /// <summary>
    /// Create virtual root with all user albums (fallback approach)
    /// </summary>
    private async Task<FolderNode> CreateVirtualRootWithAllAlbumsAsync(string userNickname, CancellationToken cancellationToken)
    {
        _logger.LogInformation("🔄 Creating virtual root with all user albums for: {UserNickname}", userNickname);

        var virtualRoot = new FolderNode
        {
            NodeId = "virtual-root",
            Name = $"{userNickname}'s Photos",
            Description = "Virtual root containing all accessible albums",
            Type = "Folder",
            FullPath = "",
            DateCreated = DateTime.Now,
            DateModified = DateTime.Now,
            HasChildren = true
        };

        try
        {
            // Use cached UserAlbums URI if available
            var albums = await GetAllUserAlbumsAsync(cancellationToken);

            _logger.LogInformation("✅ Retrieved {AlbumCount} albums for virtual root", albums.Count);

            // Convert SmugMugAlbum objects to AlbumInfo objects
            foreach (var album in albums)
            {
                var albumInfo = new AlbumInfo
                {
                    AlbumKey = album.AlbumKey,
                    NodeId = ExtractNodeIdFromUri(album.Uris?.Node?.Uri ?? "") ?? "",
                    Name = album.Name,
                    Description = album.Description ?? "",
                    UrlName = album.UrlName ?? "",
                    FullPath = $"/{album.Name}",
                    ImageCount = album.ImageCount ?? 0,
                    EstimatedSizeBytes = EstimateAlbumSize(album.ImageCount ?? 0),
                    DateCreated = album.Date ?? DateTime.MinValue,
                    DateModified = album.LastUpdated ?? DateTime.MinValue,
                    AllowDownloads = album.AllowDownloads ?? true,
                    Privacy = album.Privacy ?? "Unknown",
                    IsPublic = album.Privacy?.Equals("Public", StringComparison.OrdinalIgnoreCase) ?? false
                };

                virtualRoot.Albums.Add(albumInfo);
                virtualRoot.TotalImageCount += albumInfo.ImageCount;
                virtualRoot.TotalEstimatedSizeBytes += albumInfo.EstimatedSizeBytes;
            }

            _logger.LogInformation("✅ Virtual root created with {AlbumCount} albums, {ImageCount} total images",
                virtualRoot.Albums.Count, virtualRoot.TotalImageCount);

            return virtualRoot;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Failed to create virtual root with all albums");
            throw new InvalidOperationException($"Failed to create virtual root: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// Get folder structure using cached Folder URI for efficient access
    /// </summary>
    private async Task<FolderNode> GetFolderStructureUsingCachedFolderUriAsync(string folderUri, CancellationToken cancellationToken)
    {
        _logger.LogInformation("🗂️ Using cached Folder URI for efficient folder structure access: {Uri}", folderUri);

        var response = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugFolder>>(HttpMethod.Get, folderUri, cancellationToken);

        if (response?.Response == null)
        {
            throw new InvalidOperationException($"Failed to get folder structure from cached URI: {folderUri}");
        }

        var rootFolder = response.Response;
        _logger.LogInformation("✅ Successfully retrieved root folder from cached URI: {FolderName}", rootFolder.Name);

        // Convert SmugMugFolder to FolderNode with detailed information
        var folderNode = new FolderNode
        {
            NodeId = rootFolder.NodeId ?? "cached-root",
            Name = rootFolder.Name ?? "Root Folder",
            Description = rootFolder.Description ?? "Root folder from cached Folder URI",
            Type = "Folder",
            UrlName = rootFolder.UrlName ?? "",
            FullPath = "",
            DateCreated = rootFolder.DateAdded ?? DateTime.MinValue,
            DateModified = rootFolder.DateModified ?? DateTime.MinValue,
            HasChildren = rootFolder.HasChildren ?? false
        };

        // If the folder has children, recursively build the structure
        if (rootFolder.HasChildren == true && !string.IsNullOrEmpty(rootFolder.NodeId))
        {
            _logger.LogInformation("📁 Root folder has children, building recursive structure...");
            await BuildFolderStructureRecursivelyOptimizedAsync(folderNode, rootFolder.NodeId, "", cancellationToken);
        }

        _logger.LogInformation("🎯 Cached folder structure complete - Total: {AlbumCount} albums, {ImageCount} images, {Size} estimated size",
            folderNode.Albums.Count, folderNode.TotalImageCount, FormatBytes(folderNode.TotalEstimatedSizeBytes));

        return folderNode;
    }



    /// <summary>
    /// Get album information from a node using proper URI navigation
    /// </summary>
    private async Task<AlbumInfo> GetAlbumInfoAsync(SmugMugNode albumNode, string parentPath, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting album info for node {NodeId} ({Name})", albumNode.NodeId, albumNode.Name);

            // CRITICAL: Use the Album URI from the node response for proper navigation
            if (albumNode.Uris?.Album?.Uri != null)
            {
                _logger.LogDebug("Using Album URI from node: {AlbumUri}", albumNode.Uris.Album.Uri);
                var albumResponse = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugAlbum>>(HttpMethod.Get, albumNode.Uris.Album.Uri, cancellationToken);

                if (albumResponse?.Response != null)
                {
                    var album = albumResponse.Response;
                    _logger.LogDebug("Successfully retrieved album details: {AlbumName} with {ImageCount} images", album.Name, album.ImageCount);

                    return new AlbumInfo
                    {
                        AlbumKey = album.AlbumKey,
                        NodeId = albumNode.NodeId,
                        Name = album.Name,
                        Description = album.Description ?? "",
                        UrlName = album.UrlName ?? "",
                        FullPath = $"{parentPath}/{album.Name}",
                        ImageCount = album.ImageCount ?? 0,
                        EstimatedSizeBytes = EstimateAlbumSize(album.ImageCount ?? 0),
                        DateCreated = albumNode.DateAdded ?? DateTime.MinValue,
                        DateModified = album.LastUpdated ?? DateTime.MinValue,
                        AllowDownloads = album.AllowDownloads ?? true,
                        Privacy = album.Privacy ?? "Unknown",
                        IsPublic = album.Privacy?.Equals("Public", StringComparison.OrdinalIgnoreCase) ?? false
                    };
                }
                else
                {
                    _logger.LogWarning("Album URI returned null response for node {NodeId}", albumNode.NodeId);
                }
            }
            else
            {
                _logger.LogWarning("No Album URI found in node {NodeId} - this may indicate the node is not actually an album", albumNode.NodeId);
            }

            // Fallback: create album info from node data only
            _logger.LogDebug("Creating fallback album info from node data for {NodeId}", albumNode.NodeId);

            // Try to extract album key from Album URI even if we can't get full album details
            var albumKey = "";
            if (albumNode.Uris?.Album?.Uri != null)
            {
                var albumUri = albumNode.Uris.Album.Uri;
                albumKey = albumUri.Split('/').LastOrDefault() ?? "";
                _logger.LogDebug("Extracted album key from URI: {AlbumKey}", albumKey);
            }

            return new AlbumInfo
            {
                AlbumKey = albumKey, // Extracted from URI if available
                NodeId = albumNode.NodeId,
                Name = albumNode.Name,
                Description = albumNode.Description ?? "",
                UrlName = albumNode.UrlName ?? "",
                FullPath = $"{parentPath}/{albumNode.Name}",
                ImageCount = 0, // Will need to be fetched separately
                EstimatedSizeBytes = 0,
                DateCreated = albumNode.DateAdded ?? DateTime.MinValue,
                DateModified = albumNode.DateModified ?? DateTime.MinValue,
                AllowDownloads = true, // Default assumption
                Privacy = albumNode.Privacy ?? "Unknown",
                IsPublic = albumNode.Privacy?.Equals("Public", StringComparison.OrdinalIgnoreCase) ?? false
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get album details for node {NodeId}, creating minimal album info", albumNode.NodeId);

            // Even in error case, try to extract album key from URI if available
            var albumKey = "";
            try
            {
                if (albumNode.Uris?.Album?.Uri != null)
                {
                    var albumUri = albumNode.Uris.Album.Uri;
                    albumKey = albumUri.Split('/').LastOrDefault() ?? "";
                    _logger.LogDebug("Extracted album key from URI in error case: {AlbumKey}", albumKey);
                }
            }
            catch (Exception keyEx)
            {
                _logger.LogDebug(keyEx, "Failed to extract album key from URI in error case");
            }

            // Return minimal album info if everything fails
            return new AlbumInfo
            {
                AlbumKey = albumKey, // Try to preserve album key even in error case
                NodeId = albumNode.NodeId,
                Name = albumNode.Name ?? "Unknown Album",
                Description = "Failed to load album details",
                UrlName = albumNode.UrlName ?? "",
                FullPath = $"{parentPath}/{albumNode.Name ?? "Unknown"}",
                ImageCount = 0,
                EstimatedSizeBytes = 0,
                DateCreated = albumNode.DateAdded ?? DateTime.MinValue,
                DateModified = albumNode.DateModified ?? DateTime.MinValue,
                AllowDownloads = false, // Conservative default when we can't get details
                Privacy = "Unknown",
                IsPublic = false
            };
        }
    }





    /// <summary>
    /// Estimate album size based on image count (rough approximation)
    /// </summary>
    private static long EstimateAlbumSize(int imageCount)
    {
        // Rough estimate: 5MB per image on average
        return imageCount * 5L * 1024 * 1024;
    }

    /// <summary>
    /// Verifies that we have the correct access level for private data
    /// </summary>
    public async Task<bool> VerifyPrivateAccessAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await GetAuthenticatedUserAsync(cancellationToken);
            return !string.IsNullOrEmpty(user.NickName);
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Gets detailed information about the user's access level and permissions
    /// </summary>
    public async Task<AccessLevelInfo> GetAccessLevelInfoAsync(CancellationToken cancellationToken = default)
    {
        var accessInfo = new AccessLevelInfo();
        _logger.LogInformation("🔍 DETAILED ACCESS LEVEL ANALYSIS STARTING...");

        try
        {
            _logger.LogInformation("Step 1: Getting authenticated user information...");
            var user = await GetAuthenticatedUserAsync(cancellationToken);
            accessInfo.HasUserAccess = true;
            accessInfo.UserNickname = user.NickName;
            _logger.LogInformation("✅ User access confirmed: {UserName} ({NickName})", user.Name, user.NickName);

            // Check if user has Node URI (critical for folder access)
            if (user.Uris?.Node?.Uri != null)
            {
                _logger.LogInformation("✅ Node URI found: {NodeUri}", user.Uris.Node.Uri);
                accessInfo.HasPrivateAccess = true;
            }
            else
            {
                _logger.LogWarning("❌ NO NODE URI FOUND - This indicates LIMITED ACCESS");
                _logger.LogWarning("   This means you cannot access your folder structure");
                _logger.LogWarning("   Possible causes:");
                _logger.LogWarning("   - OAuth permissions were not granted properly");
                _logger.LogWarning("   - Different authentication session than browser");
                _logger.LogWarning("   - SmugMug account restrictions");
                accessInfo.HasPrivateAccess = false;
            }

            try
            {
                _logger.LogInformation("Step 2: Attempting to access root node...");
                var rootNode = await GetUserRootNodeAsync(cancellationToken);
                accessInfo.HasNodeAccess = true;
                accessInfo.RootNodeId = rootNode.NodeId;
                _logger.LogInformation("✅ Root node access confirmed: {NodeId}", rootNode.NodeId);
            }
            catch (Exception nodeEx)
            {
                _logger.LogWarning("❌ Root node access failed: {Error}", nodeEx.Message);
                accessInfo.HasNodeAccess = false;
            }

            // Determine overall access level
            if (accessInfo.HasPrivateAccess && accessInfo.HasNodeAccess)
            {
                accessInfo.AccessLevel = "Full";
                accessInfo.CanAccessPrivateContent = true;
                _logger.LogInformation("🎉 FULL ACCESS CONFIRMED - Can access private photos and folder structure");
            }
            else if (accessInfo.HasUserAccess)
            {
                accessInfo.AccessLevel = "Limited";
                accessInfo.CanAccessPrivateContent = false;
                _logger.LogWarning("⚠️ LIMITED ACCESS DETECTED - Can only access public content");

                var recommendations = new List<string>();
                if (!accessInfo.HasPrivateAccess)
                {
                    recommendations.Add("Re-authenticate and ensure you grant full permissions");
                    recommendations.Add("Make sure to click 'Authorize' or 'Allow' when prompted");
                }
                if (!accessInfo.HasNodeAccess)
                {
                    recommendations.Add("Cannot access folder structure - may need different OAuth scope");
                }
                accessInfo.Recommendations = recommendations.ToArray();
            }
            else
            {
                accessInfo.AccessLevel = "None";
                accessInfo.CanAccessPrivateContent = false;
                _logger.LogError("❌ NO ACCESS - Authentication failed");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Access level analysis failed");
            accessInfo.HasUserAccess = false;
            accessInfo.AccessLevel = "Unknown";
            accessInfo.CanAccessPrivateContent = false;
            accessInfo.Recommendations = new[] { "Authentication failed. Please try re-authenticating with SmugMug." };
        }

        _logger.LogInformation("🔍 ACCESS LEVEL ANALYSIS COMPLETE:");
        _logger.LogInformation("   Access Level: {AccessLevel}", accessInfo.AccessLevel);
        _logger.LogInformation("   Can Access Private Content: {CanAccess}", accessInfo.CanAccessPrivateContent);
        _logger.LogInformation("   Has User Access: {HasUser}", accessInfo.HasUserAccess);
        _logger.LogInformation("   Has Private Access: {HasPrivate}", accessInfo.HasPrivateAccess);
        _logger.LogInformation("   Has Node Access: {HasNode}", accessInfo.HasNodeAccess);

        return accessInfo;
    }

    /// <summary>
    /// Gets album information for a specific album
    /// </summary>
    public async Task<SmugMugAlbum> GetAlbumAsync(string albumKey, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting album: {AlbumKey}", albumKey);

        var url = $"{BaseApiUrl}/album/{albumKey}";
        var response = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugAlbum>>(HttpMethod.Get, url, cancellationToken);

        if (response?.Response == null)
        {
            throw new InvalidOperationException($"Failed to get album: {albumKey}");
        }

        return response.Response;
    }

    /// <summary>
    /// Gets all images in a specific album
    /// </summary>
    public async Task<List<SmugMugImage>> GetAlbumImagesAsync(string albumKey, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting images for album: {AlbumKey}", albumKey);

        var url = $"{BaseApiUrl}/album/{albumKey}!images";
        var images = new List<SmugMugImage>();

        await foreach (var image in GetPagedResultsAsync<SmugMugImage>(url, cancellationToken))
        {
            images.Add(image);
        }

        _logger.LogDebug("Found {Count} images in album: {AlbumKey}", images.Count, albumKey);
        return images;
    }

    /// <summary>
    /// Gets the size details for a specific image
    /// </summary>
    public async Task<SmugMugImageSizes> GetImageSizeDetailsAsync(string imageKey, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting size details for image: {ImageKey}", imageKey);

        var url = $"{BaseApiUrl}/image/{imageKey}!sizes";
        var response = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugImageSizes>>(HttpMethod.Get, url, cancellationToken);

        if (response?.Response == null)
        {
            throw new InvalidOperationException($"Failed to get image sizes: {imageKey}");
        }

        return response.Response;
    }

    /// <summary>
    /// Downloads image data from the specified URL
    /// </summary>
    public async Task<Stream> DownloadImageAsync(string imageUrl, CancellationToken cancellationToken = default)
    {
        return await DownloadImageAsync(imageUrl, null, cancellationToken);
    }

    /// <summary>
    /// Downloads image data from the specified URL with progress reporting
    /// </summary>
    public async Task<Stream> DownloadImageAsync(string imageUrl, IProgress<DownloadProgress>? progress, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Downloading image from: {ImageUrl}", imageUrl);

        var request = _authService.CreateAuthenticatedRequest(HttpMethod.Get, imageUrl);
        var response = await _httpClient.SendAsync(request, HttpCompletionOption.ResponseHeadersRead, cancellationToken);

        response.EnsureSuccessStatusCode();

        var contentLength = response.Content.Headers.ContentLength;
        var stream = await response.Content.ReadAsStreamAsync(cancellationToken);

        if (progress != null && contentLength.HasValue)
        {
            return new ProgressStream(stream, contentLength.Value, progress);
        }

        return stream;
    }

    /// <summary>
    /// Send an authenticated request to the SmugMug API
    /// </summary>
    private async Task<T?> SendAuthenticatedRequestAsync<T>(HttpMethod method, string url, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("🔍 Creating authenticated request for {Method} {Url}", method.Method, url);

            var request = _authService.CreateAuthenticatedRequest(method, url);
            request.Headers.Add("Accept", "application/json");

            _logger.LogDebug("📤 Sending {Method} request to: {Url}", method.Method, url);
            _logger.LogDebug("📋 Request headers: {Headers}",
                string.Join(", ", request.Headers.Select(h => $"{h.Key}: {string.Join(", ", h.Value)}")));

            var response = await _httpClient.SendAsync(request, cancellationToken);

            _logger.LogInformation("📥 Response status: {StatusCode} {ReasonPhrase}", response.StatusCode, response.ReasonPhrase);

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);
                _logger.LogError("❌ API request failed: {StatusCode} {ReasonPhrase} - {Content}",
                    response.StatusCode, response.ReasonPhrase, errorContent);

                // Special handling for OAuth signature errors
                if (errorContent.Contains("oauth_problem=signature_invalid"))
                {
                    _logger.LogError("🚨 OAUTH SIGNATURE INVALID - This indicates an issue with OAuth signature generation");
                    _logger.LogError("   URL: {Url}", url);
                    _logger.LogError("   Method: {Method}", method.Method);
                    _logger.LogError("   This might be due to:");
                    _logger.LogError("   1. Incorrect OAuth parameter encoding");
                    _logger.LogError("   2. Wrong signature base string construction");
                    _logger.LogError("   3. Mismatched consumer secret or access token secret");
                    _logger.LogError("   4. Timestamp/nonce issues");
                }

                throw new HttpRequestException($"API request failed: {response.StatusCode} {response.ReasonPhrase}");
            }

            var jsonContent = await response.Content.ReadAsStringAsync(cancellationToken);

            // Enhanced logging for debugging user data issues
            if (url.Contains("!authuser") || url.Contains("!siteuser"))
            {
                _logger.LogDebug("Raw API response from {Url}:", url);
                _logger.LogDebug("Response content: {Content}", jsonContent.Length > 1000 ? jsonContent.Substring(0, 1000) + "..." : jsonContent);
            }

            return JsonSerializer.Deserialize<T>(jsonContent, _jsonOptions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "💥 Failed to send authenticated request to: {Url}", url);
            throw;
        }
    }

    /// <summary>
    /// Get paged results from a SmugMug API endpoint
    /// SmugMug returns collections in a specific format where items are properties of the response
    /// </summary>
    private async IAsyncEnumerable<T> GetPagedResultsAsync<T>(string baseUrl, [System.Runtime.CompilerServices.EnumeratorCancellation] CancellationToken cancellationToken = default)
    {
        var start = 1;
        const int pageSize = 100;
        bool hasMore = true;

        while (hasMore && !cancellationToken.IsCancellationRequested)
        {
            var url = $"{baseUrl}?_start={start}&_count={pageSize}";
            _logger.LogDebug("Getting paged results from: {Url}", url);

            // Try different response formats that SmugMug might use
            List<T> items = new();
            SmugMugPagingInfo? pagingInfo = null;

            try
            {
                // First try: Direct array response (most common for collections)
                var directResponse = await SendAuthenticatedRequestAsync<SmugMugApiResponse<List<T>>>(HttpMethod.Get, url, cancellationToken);
                if (directResponse?.Response != null && directResponse.Response.Any())
                {
                    items = directResponse.Response;
                    _logger.LogDebug("Successfully parsed direct array response with {ItemCount} items", items.Count);
                }
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, "Direct array response parsing failed, trying collection response format");

                try
                {
                    // Second try: Collection response format
                    var collectionResponse = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugCollectionResponse<T>>>(HttpMethod.Get, url, cancellationToken);
                    if (collectionResponse?.Response != null)
                    {
                        items = ExtractItemsFromCollectionResponse<T>(collectionResponse.Response);
                        pagingInfo = collectionResponse.Response.Pages;
                        _logger.LogDebug("Successfully parsed collection response with {ItemCount} items", items.Count);
                    }
                }
                catch (Exception ex2)
                {
                    _logger.LogError(ex2, "Both response format attempts failed for URL: {Url}", url);
                    hasMore = false;
                    yield break;
                }
            }

            if (!items.Any())
            {
                _logger.LogDebug("No items found in any response format");
                hasMore = false;
                yield break;
            }

            _logger.LogDebug("Found {ItemCount} items in page starting at {Start}", items.Count, start);

            foreach (var item in items)
            {
                yield return item;
            }

            // Check pagination info if available
            if (pagingInfo != null)
            {
                hasMore = pagingInfo.HasNextPage;
                if (hasMore)
                {
                    // Extract start parameter from next page URL if available
                    if (!string.IsNullOrEmpty(pagingInfo.NextPage))
                    {
                        // Parse the next page URL to get the start parameter
                        var nextPageUrl = pagingInfo.NextPage;
                        var startIndex = nextPageUrl.IndexOf("_start=");
                        if (startIndex >= 0)
                        {
                            var startStr = nextPageUrl.Substring(startIndex + 7);
                            var endIndex = startStr.IndexOf('&');
                            if (endIndex >= 0)
                            {
                                startStr = startStr.Substring(0, endIndex);
                            }

                            if (int.TryParse(startStr, out var nextStart))
                            {
                                start = nextStart;
                            }
                            else
                            {
                                start += pageSize;
                            }
                        }
                        else
                        {
                            start += pageSize;
                        }
                    }
                    else
                    {
                        start += pageSize;
                    }
                }
            }
            else
            {
                // Fallback: check if we got a full page
                hasMore = items.Count == pageSize;
                start += pageSize;
            }
        }
    }

    /// <summary>
    /// Extract items from SmugMug collection response based on the type
    /// SmugMug returns collections with items as properties named after the type (plural)
    /// </summary>
    private List<T> ExtractItemsFromCollectionResponse<T>(SmugMugCollectionResponse<T> collectionResponse)
    {
        var items = new List<T>();
        var typeName = typeof(T).Name;

        try
        {
            // First check if Items property is populated (this is our fallback)
            if (collectionResponse.Items.Any())
            {
                items.AddRange(collectionResponse.Items);
                _logger.LogDebug("Found {ItemCount} items in Items property for type {TypeName}", items.Count, typeName);
                return items;
            }

            // SmugMug returns different collection types with items as properties
            // For collections, SmugMug typically returns multiple items, not single items
            // The single item properties (Node, Album, Image) are for individual item responses
            switch (typeName)
            {
                case "SmugMugNode":
                    // For node collections, check if there's a single Node (shouldn't happen for children)
                    if (collectionResponse.Node != null)
                    {
                        items.Add((T)(object)collectionResponse.Node);
                        _logger.LogDebug("Found single Node in collection response (unusual for children endpoint)");
                    }
                    break;

                case "SmugMugAlbum":
                    // For album collections, check if there's a single Album
                    if (collectionResponse.Album != null)
                    {
                        items.Add((T)(object)collectionResponse.Album);
                        _logger.LogDebug("Found single Album in collection response");
                    }
                    break;

                case "SmugMugImage":
                    // For image collections, check if there's a single Image
                    if (collectionResponse.Image != null)
                    {
                        items.Add((T)(object)collectionResponse.Image);
                        _logger.LogDebug("Found single Image in collection response");
                    }
                    break;

                default:
                    _logger.LogWarning("Unknown collection type: {TypeName}", typeName);
                    break;
            }

            _logger.LogDebug("Extracted {ItemCount} items from collection response for type {TypeName}", items.Count, typeName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to extract items from collection response for type: {TypeName}", typeName);
        }

        return items;
    }

    /// <summary>
    /// Format bytes into human-readable string
    /// </summary>
    private static string FormatBytes(long bytes)
    {
        if (bytes == 0) return "0 B";

        string[] sizes = { "B", "KB", "MB", "GB", "TB" };
        int order = 0;
        double size = bytes;

        while (size >= 1024 && order < sizes.Length - 1)
        {
            order++;
            size /= 1024;
        }

        return $"{size:0.##} {sizes[order]}";
    }
}
