using Microsoft.Extensions.Logging;

namespace Winmug.Core.Authentication;

/// <summary>
/// Helper class to test and compare OAuth signature generation
/// </summary>
public static class OAuthTestHelper
{
    /// <summary>
    /// Tests OAuth signature generation with sample data and compares implementations
    /// </summary>
    public static void TestOAuthSignatures(ILogger logger)
    {
        logger.LogInformation("=== OAuth Signature Test ===");
        
        // Test parameters similar to what SmugMug expects
        var testParams = new Dictionary<string, string>
        {
            ["oauth_consumer_key"] = "test_consumer_key",
            ["oauth_nonce"] = "test_nonce_12345",
            ["oauth_signature_method"] = "HMAC-SHA1",
            ["oauth_timestamp"] = "1234567890",
            ["oauth_token"] = "test_access_token",
            ["oauth_version"] = "1.0"
        };
        
        var httpMethod = "GET";
        var url = "https://api.smugmug.com/api/v2!authuser";
        var consumerSecret = "test_consumer_secret";
        var tokenSecret = "test_token_secret";
        
        logger.LogInformation("Test Parameters:");
        logger.LogInformation("HTTP Method: {Method}", httpMethod);
        logger.LogInformation("URL: {Url}", url);
        logger.LogInformation("Consumer Secret: {Secret}", consumerSecret);
        logger.LogInformation("Token Secret: {TokenSecret}", tokenSecret);
        logger.LogInformation("OAuth Parameters: {Params}", string.Join(", ", testParams.Select(kvp => $"{kvp.Key}={kvp.Value}")));
        
        try
        {
            // Test original implementation
            logger.LogInformation("--- Original Implementation ---");
            var originalSignature = OAuthSignatureGenerator.GenerateSignature(
                httpMethod, url, testParams, consumerSecret, tokenSecret);
            logger.LogInformation("Original Signature: {Signature}", originalSignature);
            
            // Test fixed implementation
            logger.LogInformation("--- Fixed Implementation ---");
            var fixedSignature = OAuthSignatureGeneratorFixed.GenerateSignature(
                httpMethod, url, testParams, consumerSecret, tokenSecret);
            logger.LogInformation("Fixed Signature: {Signature}", fixedSignature);
            
            // Compare
            if (originalSignature == fixedSignature)
            {
                logger.LogInformation("✓ Signatures match - the issue is likely elsewhere");
            }
            else
            {
                logger.LogWarning("❌ Signatures differ - this is likely the source of the authentication issue");
                logger.LogWarning("Original: {Original}", originalSignature);
                logger.LogWarning("Fixed:    {Fixed}", fixedSignature);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during OAuth signature test");
        }
        
        logger.LogInformation("=== End OAuth Signature Test ===");
    }
    
    /// <summary>
    /// Tests URL encoding differences
    /// </summary>
    public static void TestUrlEncoding(ILogger logger)
    {
        logger.LogInformation("=== URL Encoding Test ===");
        
        var testStrings = new[]
        {
            "https://api.smugmug.com/api/v2!authuser",
            "oauth_consumer_key=test&oauth_nonce=abc123",
            "special chars: !@#$%^&*()+=[]{}|;':\",./<>?",
            "unicode: café résumé naïve"
        };
        
        foreach (var testString in testStrings)
        {
            logger.LogInformation("Testing: {String}", testString);
            
            var originalEncoded = OAuthSignatureGenerator.UrlEncode(testString);
            var fixedEncoded = OAuthSignatureGeneratorFixed.PercentEncode(testString);
            
            logger.LogInformation("Original: {Original}", originalEncoded);
            logger.LogInformation("Fixed:    {Fixed}", fixedEncoded);
            
            if (originalEncoded != fixedEncoded)
            {
                logger.LogWarning("❌ Encoding differs for: {String}", testString);
            }
            else
            {
                logger.LogInformation("✓ Encoding matches");
            }
            
            logger.LogInformation("---");
        }
        
        logger.LogInformation("=== End URL Encoding Test ===");
    }
}
