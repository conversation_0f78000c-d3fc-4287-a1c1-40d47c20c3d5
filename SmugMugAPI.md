# SmugMug API v2 Reference Guide

## Overview
SmugMug API v2 uses a hierarchical structure based on **Nodes** and **Albums**. The modern approach uses the Node API rather than the legacy Folder API.

## Key Concepts

### 1. Nodes vs Folders
- **Nodes** (Modern): Unified API for folders, albums, and pages
- **Folders** (Legacy): Deprecated API for older accounts
- **Recommendation**: Use Node API for all new implementations

### 2. Node Types
- `Folder`: Container for other nodes and albums
- `Album`: Container for images
- `Page`: Custom pages
- `System Album`: Special system-created albums
- `System Page`: Special system-created pages

### 3. Node Hierarchy
- Every user has a **root node** accessible via `/api/v2/user/{nickname}!node`
- Nodes form a tree structure with parent-child relationships
- Maximum folder depth: 5 levels (albums don't count toward depth)

## Core API Endpoints

### Authentication
```
OAuth 1.0a Endpoints (Use secure.smugmug.com for OAuth):
- Request Token: https://secure.smugmug.com/services/oauth/1.0a/getRequestToken
- Authorization: https://secure.smugmug.com/services/oauth/1.0a/authorize
- Access Token: https://secure.smugmug.com/services/oauth/1.0a/getAccessToken

Required Parameters for Authorization URL:
- Access=Full (for private data access)
- Permissions=Read (for downloading)
- oauth_callback=oob (for desktop applications)

CRITICAL: Use secure.smugmug.com for OAuth endpoints, api.smugmug.com for API calls
```

### User Information
```
GET /api/v2/user/{nickname}                    # Basic user info
GET /api/v2/user/{nickname}!profile            # User profile
GET /api/v2!authuser                           # Authenticated user info (CORRECT)
GET /api/v2!siteuser                           # Site user info (alternative)
```

### Getting User's Root Node
```
# Method 1: From User response (recommended)
GET /api/v2!authuser -> Response.User.Uris.Node.Uri

# Method 2: Direct access (if you know nickname)
GET /api/v2/user/{nickname} -> Response.User.Uris.Node.Uri
```

### Node Navigation
```
GET /api/v2/node/{nodeId}                      # Get specific node
GET /api/v2/node/{nodeId}!children             # Get child nodes
GET /api/v2/node/{nodeId}!parent               # Get parent node
GET /api/v2/node/{nodeId}!parents              # Get all parent nodes (breadcrumb)

# With expansions for efficiency
GET /api/v2/node/{nodeId}!children?_expand=Album  # Expand album details
GET /api/v2/node/{nodeId}?_expand=ChildNodes       # Get node with children
```

### Advanced Queries with Expansions
```
# Get node with all child nodes expanded
GET /api/v2/node/{nodeId}?_expand=ChildNodes

# Get multiple levels deep
GET /api/v2/node/{nodeId}?_expand=ChildNodes.ChildNodes

# Get folder with albums and their image counts
GET /api/v2/node/{nodeId}!children?_expand=Album&Type=Album
```

### Album Operations
```
GET /api/v2/album/{albumKey}                   # Get album details
GET /api/v2/album/{albumKey}!images            # Get album images
GET /api/v2/album/{albumKey}!download          # Download album
```

### Legacy Folder API (Deprecated)
```
GET /api/v2/folder/user/{nickname}             # Root folder
GET /api/v2/folder/user/{nickname}/{path}      # Specific folder
GET /api/v2/folder/user/{nickname}!folders     # Child folders
GET /api/v2/api/folder/user/{nickname}!folderlist

```
### Album list
GET /api/v2/user/{nickname}!albums      # all albums

## Important Node Fields

### Core Properties
- `NodeID`: Unique identifier (never changes)
- `Type`: "Folder", "Album", "Page", etc.
- `Name`: Human-readable name
- `UrlName`: URL-friendly name
- `UrlPath`: Full path from root
- `HasChildren`: Boolean indicating if node has children
- `IsRoot`: Boolean indicating if this is the root node

### Dates
- `DateAdded`: When node was created
- `DateModified`: Last modification time

### Security
- `Privacy`: "Public", "Unlisted", "Private"
- `SecurityType`: "None", "Password", "GrantAccess"
- `EffectivePrivacy`: Effective privacy (considering parent restrictions)
- `EffectiveSecurityType`: Effective security (considering parent restrictions)

### Navigation URIs
- `ChildNodes`: URI to get child nodes
- `ParentNode`: URI to get parent node
- `ParentNodes`: URI to get all parents (breadcrumb)
- `Album`: URI to album details (if node is album)

## Album-Specific Fields

### Core Properties
- `AlbumKey`: Unique album identifier
- `ImageCount`: Number of images in album
- `ImagesLastUpdated`: Last time images were modified

### Download Settings
- `AllowDownloads`: Whether downloads are allowed
- `HasDownloadPassword`: Whether download requires password
- `MaxPhotoDownloadSize`: Maximum download size allowed

### Display Settings
- `SortMethod`: How images are sorted
- `SortDirection`: "Ascending" or "Descending"

## Folder Structure Retrieval Strategy

### CRITICAL: Correct API Flow for Full Access

#### Step 1: Get Authenticated User
```http
GET /api/v2!authuser
Authorization: OAuth oauth_consumer_key="...", oauth_token="...", oauth_signature="..."
```

#### Step 2: Extract Root Node URI
```json
{
  "Response": {
    "User": {
      "Uris": {
        "Node": {
          "Uri": "/api/v2/node/ABC123"  // This is the root node URI
        }
      }
    }
  }
}
```

#### Step 3: Get Root Node Details
```http
GET /api/v2/node/ABC123
```

#### Step 4: Traverse Hierarchy Recursively
```http
GET /api/v2/node/{nodeId}!children
```

#### Step 5: Build Tree Structure
- Start from authenticated user's root node
- Recursively fetch children for each folder node
- Collect album information for each album node
- Calculate totals (image counts, sizes) by aggregating children

### Why Previous Approaches Failed
1. **Wrong User Endpoint**: Using `/api/v2/user/{nickname}!node` instead of `/api/v2!authuser`
2. **Missing OAuth Headers**: Not including proper OAuth authorization
3. **🚨 CRITICAL: Incorrect Base URLs**: Using `api.smugmug.com` for OAuth instead of `secure.smugmug.com`
4. **Access Level Issues**: Not properly requesting `Access=Full` during OAuth

### CRITICAL CONFIGURATION FIX
**OAuth endpoints MUST use `secure.smugmug.com`, API calls use `api.smugmug.com`:**

```json
{
  "SmugMugOAuth": {
    "RequestTokenUrl": "https://secure.smugmug.com/services/oauth/1.0a/getRequestToken",
    "AuthorizeUrl": "https://secure.smugmug.com/services/oauth/1.0a/authorize",
    "AccessTokenUrl": "https://secure.smugmug.com/services/oauth/1.0a/getAccessToken",
    "ApiBaseUrl": "https://api.smugmug.com/api/v2",
    "Access": "Full",
    "Permissions": "Read"
  }
}
```

## Response Structure Examples

### Node Response
```json
{
  "Response": {
    "Node": {
      "NodeID": "XWx8t",
      "Type": "Folder",
      "Name": "SmugMug",
      "UrlName": "SmugMug",
      "UrlPath": "/SmugMug",
      "HasChildren": true,
      "IsRoot": false,
      "DateAdded": "2013-07-13T22:44:11+00:00",
      "DateModified": "2021-07-18T03:58:14+00:00",
      "Uris": {
        "ChildNodes": {
          "Uri": "/api/v2/node/XWx8t!children"
        }
      }
    }
  }
}
```

### Album Response
```json
{
  "Response": {
    "Album": {
      "AlbumKey": "SJT3DX",
      "NodeID": "ZsfFs",
      "Name": "Prints from Bay Photo",
      "UrlName": "Bay-Photo-Prints",
      "ImageCount": 6,
      "AllowDownloads": true,
      "Uris": {
        "AlbumImages": {
          "Uri": "/api/v2/album/SJT3DX!images"
        }
      }
    }
  }
}
```

## Best Practices

### 1. Use Node API
- Prefer Node API over legacy Folder API
- Node API works with both old and new SmugMug accounts

### 2. Handle Pagination
- Use `_start` and `_count` parameters for large result sets
- Default page size is typically 100 items

### 3. Optimize Requests
- Use `_expand` parameter to get related data in single request
- Use `_filter` parameter to limit response fields

### 4. Error Handling
- Check `EffectivePrivacy` and `EffectiveSecurityType` for access restrictions
- Handle 403 errors for private content
- Implement retry logic for rate limits

### 5. Caching Strategy
- Cache node hierarchy to reduce API calls
- Use `DateModified` to detect changes
- Implement incremental updates for large structures

## Common Issues and Solutions

### 1. Access Denied (403)
- Ensure OAuth has "Full" access level
- Check if content is private and requires authentication
- Verify user has permission to access the content

### 2. Empty Responses
- Check if user has migrated to New SmugMug
- Use Node API instead of Folder API for better compatibility
- Verify the node/album actually exists

### 3. Performance Issues
- Implement pagination for large folders
- Use parallel requests for independent operations
- Cache frequently accessed data

## Rate Limits
- SmugMug enforces rate limits on API calls
- Implement exponential backoff for rate limit errors
- Consider using bulk operations where available

## Folder Structure Implementation Guide

### Recommended Approach
1. **Start with authenticated user**: `GET /api/v2/user/!authuser`
2. **Get root node**: Follow `Node` URI from user response
3. **Build hierarchy**: Use `ChildNodes` URI to traverse structure
4. **Handle albums**: Use `Album` URI for album-specific data
5. **Calculate totals**: Aggregate image counts and sizes

### Sample Implementation Flow
```csharp
// 1. Get authenticated user and root node
var user = await GetAuthenticatedUserAsync();
var rootNodeUri = user.Uris?.Node?.Uri;
var rootNode = await GetNodeAsync(rootNodeUri);

// 2. Build folder structure recursively
var folderStructure = await BuildFolderStructureAsync(rootNode);

// 3. For each node, get children and process
foreach (var child in await GetChildNodesAsync(node.NodeId))
{
    if (child.Type == "Folder")
    {
        // Recurse into subfolder
        var subfolder = await BuildFolderStructureAsync(child);
    }
    else if (child.Type == "Album")
    {
        // Get album details and image count
        var album = await GetAlbumAsync(child.AlbumKey);
        // Add to current folder's album list
    }
}
```

### Error Recovery Strategies
- **Private Content**: Check `EffectivePrivacy` before accessing
- **Missing Permissions**: Gracefully handle 403 errors
- **Legacy Accounts**: Fall back to Folder API if Node API fails
- **Network Issues**: Implement retry with exponential backoff

### Performance Optimizations
- **Parallel Processing**: Fetch sibling nodes in parallel
- **Lazy Loading**: Load folder contents on-demand
- **Caching**: Cache node information to reduce API calls
- **Pagination**: Handle large folders with paging
