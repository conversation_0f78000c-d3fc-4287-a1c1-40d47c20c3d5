using System.Text.Json.Serialization;

namespace Winmug.Core.Models;

/// <summary>
/// Represents a SmugMug album with additional album-specific properties
/// </summary>
public class SmugMugAlbum
{
    [JsonPropertyName("AlbumKey")]
    public string AlbumKey { get; set; } = string.Empty;

    [JsonPropertyName("Name")]
    public string Name { get; set; } = string.Empty;

    [JsonPropertyName("Description")]
    public string? Description { get; set; }

    [JsonPropertyName("UrlName")]
    public string? UrlName { get; set; }

    [JsonPropertyName("UrlPath")]
    public string? UrlPath { get; set; }

    [JsonPropertyName("WebUri")]
    public string WebUri { get; set; } = string.Empty;

    [JsonPropertyName("Uri")]
    public string Uri { get; set; } = string.Empty;

    [JsonPropertyName("Date")]
    public DateTime? Date { get; set; }

    [JsonPropertyName("LastUpdated")]
    public DateTime? LastUpdated { get; set; }

    [JsonPropertyName("ImagesLastUpdated")]
    public DateTime? ImagesLastUpdated { get; set; }

    [JsonPropertyName("Privacy")]
    public string? Privacy { get; set; }

    [JsonPropertyName("SmugSearchable")]
    public string? SmugSearchable { get; set; }

    [JsonPropertyName("WorldSearchable")]
    public string? WorldSearchable { get; set; }

    [JsonPropertyName("PasswordHint")]
    public string? PasswordHint { get; set; }

    [JsonPropertyName("External")]
    public bool? External { get; set; }

    [JsonPropertyName("ImageCount")]
    public int? ImageCount { get; set; }

    [JsonPropertyName("AllowDownloads")]
    public bool? AllowDownloads { get; set; }

    [JsonPropertyName("Uris")]
    public SmugMugAlbumUris? Uris { get; set; }

    // Helper properties
    public string DisplayName => !string.IsNullOrEmpty(Name) ? Name : UrlName ?? AlbumKey;
}

/// <summary>
/// Contains URIs for SmugMug album-related resources
/// </summary>
public class SmugMugAlbumUris
{
    [JsonPropertyName("AlbumImages")]
    public SmugMugUriInfo? AlbumImages { get; set; }

    [JsonPropertyName("User")]
    public SmugMugUriInfo? User { get; set; }

    [JsonPropertyName("Node")]
    public SmugMugUriInfo? Node { get; set; }

    [JsonPropertyName("HighlightImage")]
    public SmugMugUriInfo? HighlightImage { get; set; }
}

/// <summary>
/// Response wrapper for SmugMug albums collection
/// </summary>
public class SmugMugAlbumsResponse
{
    [JsonPropertyName("Album")]
    public List<SmugMugAlbum> Album { get; set; } = new();
}
