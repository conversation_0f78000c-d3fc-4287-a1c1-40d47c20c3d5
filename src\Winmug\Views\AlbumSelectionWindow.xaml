<Window x:Class="Winmug.Views.AlbumSelectionWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="Select Albums to Download" 
        Height="700" Width="900"
        MinHeight="600" MinWidth="800"
        WindowStartupLocation="CenterOwner">
    
    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <Style x:Key="AlbumListItemStyle" TargetType="ListViewItem">
            <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
            <Setter Property="Padding" Value="5"/>
            <Setter Property="Margin" Value="1"/>
        </Style>
    </Window.Resources>

    <Grid Margin="15">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Margin="0,0,0,15">
            <TextBlock Text="Select Albums to Download" 
                       FontSize="20" FontWeight="Bold" 
                       HorizontalAlignment="Center"/>
            <TextBlock Text="Choose which albums you want to download from your SmugMug account" 
                       FontSize="12" Foreground="Gray" 
                       HorizontalAlignment="Center" Margin="0,5,0,0"/>
        </StackPanel>

        <!-- Summary Information -->
        <Border Grid.Row="1" Background="#F5F5F5" Padding="10" Margin="0,0,0,10" CornerRadius="5">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="Number of albums found: " FontWeight="Bold"/>
                    <TextBlock Text="{Binding TotalAlbumCount}" FontWeight="Bold"/>
                    <TextBlock Text=" (" Margin="5,0,0,0"/>
                    <TextBlock Text="{Binding TotalSelectedSize}" FontWeight="Bold"/>
                    <TextBlock Text=")"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal" Margin="20,0">
                    <Button Content="🔄 Refresh" Command="{Binding LoadAlbumsCommand}" 
                            Padding="8,4" Margin="0,0,5,0"/>
                    <CheckBox Content="Hide albums" IsChecked="{Binding HideAlbums}" 
                              VerticalAlignment="Center"/>
                </StackPanel>

                <Button Grid.Column="2" Content="Change" Padding="8,4"/>
            </Grid>
        </Border>

        <!-- Search and Filter Controls -->
        <Grid Grid.Row="2" Margin="0,0,0,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <TextBox Grid.Column="0" Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                     Padding="8,5" FontSize="14"
                     ToolTip="Search albums..."/>

            <StackPanel Grid.Column="1" Orientation="Horizontal" Margin="10,0">
                <TextBlock Text="Save location: " VerticalAlignment="Center" Margin="0,0,5,0"/>
                <TextBlock Text="Please select a destination directory" 
                           VerticalAlignment="Center" FontStyle="Italic" Foreground="Gray"/>
            </StackPanel>

            <ComboBox Grid.Column="2" Width="120" Margin="10,0,0,0">
                <ComboBoxItem Content="Originals" IsSelected="True"/>
                <ComboBoxItem Content="Large"/>
                <ComboBoxItem Content="Medium"/>
            </ComboBox>
        </Grid>

        <!-- Albums List -->
        <Border Grid.Row="3" BorderBrush="#CCCCCC" BorderThickness="1" CornerRadius="3">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Albums Header -->
                <Border Grid.Row="0" Background="#E8E8E8" Padding="10,8">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0" Orientation="Horizontal">
                            <TextBlock Text="Albums" FontWeight="Bold" FontSize="14"/>
                            <TextBlock Text="Selected: " Margin="20,0,0,0"/>
                            <TextBlock Text="{Binding TotalSelectedSize}" FontWeight="Bold"/>
                        </StackPanel>

                        <StackPanel Grid.Column="2" Orientation="Horizontal">
                            <Button Content="Select all" Command="{Binding SelectAllCommand}" 
                                    Padding="8,4" Margin="0,0,5,0" FontSize="12"/>
                            <Button Content="Deselect all" Command="{Binding DeselectAllCommand}" 
                                    Padding="8,4" FontSize="12"/>
                        </StackPanel>
                    </Grid>
                </Border>

                <!-- Albums ListView -->
                <ListView Grid.Row="1" ItemsSource="{Binding AlbumsView}" 
                          ItemContainerStyle="{StaticResource AlbumListItemStyle}"
                          ScrollViewer.HorizontalScrollBarVisibility="Disabled">
                    <ListView.ItemTemplate>
                        <DataTemplate>
                            <Grid Margin="5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <!-- Checkbox -->
                                <CheckBox Grid.Column="0" IsChecked="{Binding IsSelected}" 
                                          VerticalAlignment="Center" Margin="0,0,10,0"/>

                                <!-- Album Icon -->
                                <TextBlock Grid.Column="1" Text="{Binding AlbumTypeIcon}" 
                                           FontSize="16" VerticalAlignment="Center" Margin="0,0,8,0"/>

                                <!-- Album Info -->
                                <StackPanel Grid.Column="2" VerticalAlignment="Center">
                                    <TextBlock Text="{Binding DisplayName}" FontWeight="Bold" FontSize="14"/>
                                    <TextBlock Text="{Binding Description}" FontSize="12"
                                               Foreground="Gray" TextTrimming="CharacterEllipsis">
                                        <TextBlock.Style>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="Visibility" Value="Visible"/>
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding Description}" Value="">
                                                        <Setter Property="Visibility" Value="Collapsed"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Description}" Value="{x:Null}">
                                                        <Setter Property="Visibility" Value="Collapsed"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </TextBlock.Style>
                                    </TextBlock>
                                    <StackPanel Orientation="Horizontal" Margin="0,2,0,0">
                                        <TextBlock Text="{Binding ImageCount}" FontSize="11" Foreground="Gray"/>
                                        <TextBlock Text=" images" FontSize="11" Foreground="Gray"/>
                                        <TextBlock Text=" • " FontSize="11" Foreground="Gray" Margin="5,0"/>
                                        <TextBlock Text="{Binding EstimatedSize}" FontSize="11" Foreground="Gray"/>
                                    </StackPanel>
                                </StackPanel>

                                <!-- Album Status -->
                                <StackPanel Grid.Column="3" VerticalAlignment="Center" HorizontalAlignment="Right">
                                    <TextBlock Text="✓" FontSize="12" Foreground="Green" FontWeight="Bold"
                                               Visibility="{Binding IsSelected, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                                </StackPanel>
                            </Grid>
                        </DataTemplate>
                    </ListView.ItemTemplate>
                </ListView>

                <!-- Loading Overlay -->
                <Border Grid.Row="1" Background="#80FFFFFF" 
                        Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                        <ProgressBar IsIndeterminate="True" Width="200" Height="20" Margin="0,0,0,10"/>
                        <TextBlock Text="{Binding StatusMessage}" HorizontalAlignment="Center" FontWeight="Bold"/>
                    </StackPanel>
                </Border>
            </Grid>
        </Border>

        <!-- Selection Summary -->
        <Border Grid.Row="4" Background="#F0F8FF" Padding="10" Margin="0,10,0,10" CornerRadius="3">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <TextBlock Text="Selected: " FontWeight="Bold"/>
                <TextBlock Text="{Binding SelectedAlbumCount}" FontWeight="Bold" Margin="0,0,5,0"/>
                <TextBlock Text="albums with " FontWeight="Bold"/>
                <TextBlock Text="{Binding TotalSelectedImageCount}" FontWeight="Bold" Margin="0,0,5,0"/>
                <TextBlock Text="images (" FontWeight="Bold"/>
                <TextBlock Text="{Binding TotalSelectedSize}" FontWeight="Bold"/>
                <TextBlock Text=")" FontWeight="Bold"/>
            </StackPanel>
        </Border>

        <!-- Action Buttons -->
        <StackPanel Grid.Row="5" Orientation="Horizontal" HorizontalAlignment="Right">
            <Button Content="Cancel" IsCancel="True" Padding="15,8" Margin="0,0,10,0" MinWidth="80"/>
            <Button Content="Done" IsDefault="True" Padding="15,8" MinWidth="80" 
                    Click="DoneButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
