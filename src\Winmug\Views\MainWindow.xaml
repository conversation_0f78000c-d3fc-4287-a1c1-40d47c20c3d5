<Window x:Class="Winmug.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="Winmug - SmugMug Photo Downloader" 
        Height="600" Width="900"
        MinHeight="500" MinWidth="700"
        WindowStartupLocation="CenterScreen">
    
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Margin="0,0,0,20">
            <TextBlock Text="Winmug - SmugMug Photo Downloader" 
                       FontSize="24" FontWeight="Bold" 
                       HorizontalAlignment="Center"/>
            <TextBlock Text="Download your entire SmugMug photo library to your local computer" 
                       FontSize="14" Foreground="Gray" 
                       HorizontalAlignment="Center" Margin="0,5,0,0"/>
        </StackPanel>

        <!-- Authentication Section -->
        <GroupBox Grid.Row="1" Header="Authentication" Margin="0,0,0,10">
            <StackPanel Margin="10">
                <TextBlock Text="{Binding AuthenticationStatus}" 
                           FontWeight="Bold" Margin="0,0,0,10"/>
                
                <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                    <Button Content="Authenticate with SmugMug" 
                            Command="{Binding InitiateAuthenticationCommand}"
                            IsEnabled="{Binding IsAuthenticated, Converter={StaticResource InverseBooleanConverter}}"
                            Margin="0,0,10,0" Padding="10,5"/>
                    
                    <Button Content="Logout" 
                            Command="{Binding LogoutCommand}"
                            IsEnabled="{Binding IsAuthenticated}"
                            Margin="0,0,10,0" Padding="10,5"/>
                    
                    <Button Content="Test API Connection"
                            Command="{Binding TestApiConnectionCommand}"
                            IsEnabled="{Binding IsAuthenticated}"
                            Margin="0,0,10,0" Padding="10,5"/>

                    <Button Content="Load Folder Structure"
                            Command="{Binding LoadFolderStructureCommand}"
                            IsEnabled="{Binding IsAuthenticated}"
                            Padding="10,5"
                            Margin="10,0,0,0"/>

                    <Button Content="Test OAuth"
                            Command="{Binding TestOAuthCommand}"
                            IsEnabled="{Binding IsAuthenticated, Converter={StaticResource InverseBooleanConverter}}"
                            Padding="10,5"/>
                </StackPanel>

                <!-- Verification Code Input (shown when waiting for verification code) -->
                <StackPanel Orientation="Horizontal" Margin="0,0,0,10"
                            Visibility="{Binding IsWaitingForVerificationCode, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <TextBlock Text="6-digit verification code:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBox x:Name="VerificationCodeTextBox" Width="150" Margin="0,0,10,0"
                             ToolTip="Enter the 6-digit code from SmugMug after logging in"/>
                    <Button Content="Complete Authentication"
                            Command="{Binding CompleteAuthenticationCommand}"
                            CommandParameter="{Binding ElementName=VerificationCodeTextBox, Path=Text}"
                            Padding="10,5"/>
                </StackPanel>
            </StackPanel>
        </GroupBox>

        <!-- Folder Structure Section -->
        <GroupBox Grid.Row="2" Header="Folder Structure" Margin="0,0,0,10"
                  Visibility="{Binding IsFolderStructureLoaded, Converter={StaticResource BooleanToVisibilityConverter}}">
            <Grid Margin="10">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Summary -->
                <TextBlock Grid.Row="0" Margin="0,0,0,10">
                    <Run Text="Total Images: "/>
                    <Run Text="{Binding FolderStructure.TotalImageCount}" FontWeight="Bold"/>
                    <Run Text=" | Total Size: "/>
                    <Run Text="{Binding FolderStructure.TotalEstimatedSize}" FontWeight="Bold"/>
                </TextBlock>

                <!-- Folder Tree -->
                <TreeView Grid.Row="1" x:Name="FolderTreeView" Height="200"
                          ItemsSource="{Binding FolderStructure.Children}">
                    <TreeView.ItemTemplate>
                        <HierarchicalDataTemplate ItemsSource="{Binding Children}">
                            <StackPanel Orientation="Horizontal">
                                <CheckBox IsChecked="{Binding IsSelected}" Margin="0,0,5,0"/>
                                <TextBlock Text="{Binding DisplayText}"/>
                            </StackPanel>
                        </HierarchicalDataTemplate>
                    </TreeView.ItemTemplate>
                </TreeView>

                <!-- Download Selected Button -->
                <Button Grid.Row="2" Content="Download Selected Folders"
                        Margin="0,10,0,0" Padding="10,5"
                        HorizontalAlignment="Left"/>
            </Grid>
        </GroupBox>

        <!-- Download Configuration Section -->
        <GroupBox Grid.Row="3" Header="Download Configuration" Margin="0,0,0,10">
            <StackPanel Margin="10">
                <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                    <TextBlock Text="Target Directory:" VerticalAlignment="Center" Width="120"/>
                    <TextBox Text="{Binding TargetDirectory}" 
                             IsReadOnly="True" 
                             Width="400" Margin="0,0,10,0"/>
                    <Button Content="Browse..." 
                            Command="{Binding SelectTargetDirectoryCommand}"
                            Padding="10,5"/>
                </StackPanel>

                <StackPanel Orientation="Horizontal">
                    <Button Content="Select Albums to Download"
                            Command="{Binding SelectAlbumsForDownloadCommand}"
                            IsEnabled="{Binding IsAuthenticated}"
                            Margin="0,0,10,0" Padding="15,8"
                            Background="#4CAF50" Foreground="White"/>
                    <Button Content="Download All"
                            Command="{Binding StartDownloadCommand}"
                            IsEnabled="{Binding CanStartDownload}"
                            Margin="0,0,10,0" Padding="15,8"/>
                    <Button Content="Pause"
                            Command="{Binding PauseDownloadCommand}"
                            IsEnabled="{Binding CanPauseDownload}"
                            Margin="0,0,10,0" Padding="15,8"/>
                    <Button Content="Resume"
                            Command="{Binding ResumeDownloadCommand}"
                            IsEnabled="{Binding CanResumeDownload}"
                            Margin="0,0,10,0" Padding="15,8"/>
                    <Button Content="Cancel"
                            Command="{Binding CancelDownloadCommand}"
                            IsEnabled="{Binding CanCancelDownload}"
                            Padding="15,8"/>
                </StackPanel>
            </StackPanel>
        </GroupBox>

        <!-- Progress and Log Section -->
        <GroupBox Grid.Row="4" Header="Status and Log">
            <Grid Margin="10">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Status -->
                <TextBlock Grid.Row="0" Text="{Binding StatusMessage}" 
                           FontWeight="Bold" Margin="0,0,0,10"/>

                <!-- Progress Bars -->
                <StackPanel Grid.Row="1" Margin="0,0,0,10">
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                        <TextBlock Text="Overall Progress:" Margin="0,0,10,0"/>
                        <TextBlock Text="{Binding ProgressText}" FontWeight="Bold"/>
                        <TextBlock Text="{Binding DownloadSpeed, StringFormat=' - {0}'}" Margin="10,0,0,0"/>
                        <TextBlock Text="{Binding EstimatedTimeRemaining, StringFormat=' - ETA: {0}'}" Margin="10,0,0,0"/>
                    </StackPanel>
                    <ProgressBar Height="20" Value="{Binding OverallProgress}" Maximum="100" Margin="0,0,0,10"/>

                    <TextBlock Text="Current File:" Margin="0,0,0,5"/>
                    <ProgressBar Height="20" Value="{Binding CurrentFileProgress}" Maximum="100"/>
                </StackPanel>

                <!-- Log Messages -->
                <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto">
                    <ListBox ItemsSource="{Binding LogMessages}" 
                             ScrollViewer.HorizontalScrollBarVisibility="Auto"
                             FontFamily="Consolas" FontSize="12">
                        <ListBox.ItemContainerStyle>
                            <Style TargetType="ListBoxItem">
                                <Setter Property="Padding" Value="2"/>
                                <Setter Property="Margin" Value="0"/>
                            </Style>
                        </ListBox.ItemContainerStyle>
                    </ListBox>
                </ScrollViewer>
            </Grid>
        </GroupBox>

        <!-- Status Bar -->
        <StatusBar Grid.Row="5">
            <StatusBarItem>
                <TextBlock Text="{Binding StatusMessage}"/>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <TextBlock Text="Ready"/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
