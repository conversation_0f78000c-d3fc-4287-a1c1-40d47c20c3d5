using Winmug.Core.Models;

namespace Winmug.Core.Services;

/// <summary>
/// Client for interacting with the SmugMug API - SYNCHRONOUS VERSION
/// </summary>
public interface ISmugMugApiClient
{
    /// <summary>
    /// Gets the authenticated user information
    /// </summary>
    SmugMugUser GetAuthenticatedUser(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get the complete folder structure with album counts and size estimates
    /// SIMPLIFIED: Just calls /api/v2/user/{nickname}!albums after user profile
    /// </summary>
    FolderNode GetFolderStructure(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all user albums using /api/v2/user/{nickname}!albums endpoint
    /// </summary>
    List<SmugMugAlbum> GetAllUserAlbums(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all images in a specific album
    /// </summary>
    List<SmugMugImage> GetAlbumImages(string albumKey, CancellationToken cancellationToken = default);

    /// <summary>
    /// Downloads image data from the specified URL
    /// </summary>
    Stream DownloadImage(string imageUrl, CancellationToken cancellationToken = default);

    /// <summary>
    /// Downloads image data from the specified URL with progress reporting
    /// </summary>
    Stream DownloadImage(string imageUrl, IProgress<DownloadProgress>? progress, CancellationToken cancellationToken = default);

    /// <summary>
    /// Clear the URI cache (call when user logs out or session ends)
    /// </summary>
    void ClearUriCache();
}

/// <summary>
/// Represents download progress information
/// </summary>
public class DownloadProgress
{
    public long BytesDownloaded { get; set; }
    public long? TotalBytes { get; set; }
    public double? ProgressPercentage => TotalBytes.HasValue && TotalBytes > 0 
        ? (double)BytesDownloaded / TotalBytes.Value * 100 
        : null;
    public TimeSpan Elapsed { get; set; }
    public double? BytesPerSecond { get; set; }
    public TimeSpan? EstimatedTimeRemaining { get; set; }
}
