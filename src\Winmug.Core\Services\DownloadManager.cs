using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Diagnostics;
using Winmug.Core.Models;

namespace Winmug.Core.Services;

/// <summary>
/// Implementation of the download manager for SmugMug photos
/// </summary>
public class DownloadManager : IDownloadManager, IDisposable
{
    private readonly ISmugMugApiClient _apiClient;
    private readonly ILogger<DownloadManager> _logger;
    private readonly SemaphoreSlim _downloadSemaphore;
    private readonly ConcurrentBag<DownloadError> _errors;
    
    private CancellationTokenSource? _cancellationTokenSource;
    private readonly Stopwatch _stopwatch;
    private DownloadStatus _status;
    private DownloadStatistics _statistics;
    private string? _targetDirectory;

    // Fields for Interlocked operations
    private int _processedAlbums;
    private int _downloadedPhotos;
    private int _failedPhotos;
    private long _downloadedBytes;

    public event EventHandler<DownloadProgressEventArgs>? ProgressUpdated;
    public event EventHandler<DownloadStatusChangedEventArgs>? StatusChanged;
    public event EventHandler<DownloadErrorEventArgs>? ErrorOccurred;

    public DownloadStatus Status => _status;
    public DownloadStatistics Statistics => _statistics;

    private const int MaxConcurrentDownloads = 3;

    public DownloadManager(ISmugMugApiClient apiClient, ILogger<DownloadManager> logger)
    {
        _apiClient = apiClient;
        _logger = logger;
        _downloadSemaphore = new SemaphoreSlim(MaxConcurrentDownloads, MaxConcurrentDownloads);
        _errors = new ConcurrentBag<DownloadError>();
        _stopwatch = new Stopwatch();
        _statistics = new DownloadStatistics();
        _status = DownloadStatus.NotStarted;
    }

    public async Task StartDownloadAsync(string targetDirectory, CancellationToken cancellationToken = default)
    {
        if (_status == DownloadStatus.Downloading)
        {
            throw new InvalidOperationException("Download is already in progress");
        }

        try
        {
            _targetDirectory = targetDirectory;
            _cancellationTokenSource = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
            
            SetStatus(DownloadStatus.Discovering, "Discovering albums and photos...");
            _stopwatch.Restart();
            
            // Clear previous statistics and errors
            _statistics = new DownloadStatistics();
            _errors.Clear();

            // Reset counters
            _processedAlbums = 0;
            _downloadedPhotos = 0;
            _failedPhotos = 0;
            _downloadedBytes = 0;

            // Create target directory if it doesn't exist
            Directory.CreateDirectory(targetDirectory);

            // Discover all albums and photos
            var allNodes = await DiscoverContentAsync(_cancellationTokenSource.Token);
            var albums = allNodes.Where(n => n.IsAlbum).ToList();

            _statistics.TotalAlbums = albums.Count;
            _logger.LogInformation("Discovered {AlbumCount} albums", albums.Count);

            // Count total photos
            await CountPhotosAsync(albums, _cancellationTokenSource.Token);
            
            SetStatus(DownloadStatus.Downloading, "Starting photo downloads...");

            // Download all photos
            await DownloadAllPhotosAsync(albums, _cancellationTokenSource.Token);

            SetStatus(DownloadStatus.Completed, "Download completed successfully");
        }
        catch (OperationCanceledException)
        {
            SetStatus(DownloadStatus.Cancelled, "Download was cancelled");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Download failed");
            SetStatus(DownloadStatus.Error, $"Download failed: {ex.Message}");
            throw;
        }
        finally
        {
            _stopwatch.Stop();
            _statistics.ElapsedTime = _stopwatch.Elapsed;
        }
    }

    public async Task StartSelectiveDownloadAsync(string targetDirectory, List<SelectableAlbum> selectedAlbums, CancellationToken cancellationToken = default)
    {
        if (_status == DownloadStatus.Downloading)
        {
            throw new InvalidOperationException("Download is already in progress");
        }

        try
        {
            _targetDirectory = targetDirectory;
            _cancellationTokenSource = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);

            SetStatus(DownloadStatus.Discovering, "Preparing selected albums for download...");
            _stopwatch.Restart();

            // Clear previous statistics and errors
            _statistics = new DownloadStatistics();
            _errors.Clear();

            // Reset counters
            _processedAlbums = 0;
            _downloadedPhotos = 0;
            _failedPhotos = 0;
            _downloadedBytes = 0;

            // Create target directory if it doesn't exist
            Directory.CreateDirectory(targetDirectory);

            // Convert selected albums to SmugMugNode format for download processing
            var albumNodes = await ConvertSelectedAlbumsToNodes(selectedAlbums, _cancellationTokenSource.Token);

            _statistics.TotalAlbums = albumNodes.Count;
            _logger.LogInformation("Starting selective download of {AlbumCount} albums", albumNodes.Count);

            // Count total photos in selected albums
            await CountPhotosAsync(albumNodes, _cancellationTokenSource.Token);

            SetStatus(DownloadStatus.Downloading, "Starting photo downloads...");

            // Download all photos from selected albums
            await DownloadAllPhotosAsync(albumNodes, _cancellationTokenSource.Token);

            SetStatus(DownloadStatus.Completed, "Selective download completed successfully");
        }
        catch (OperationCanceledException)
        {
            SetStatus(DownloadStatus.Cancelled, "Download was cancelled");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Selective download failed");
            SetStatus(DownloadStatus.Error, $"Selective download failed: {ex.Message}");
            throw;
        }
        finally
        {
            _stopwatch.Stop();
            _statistics.ElapsedTime = _stopwatch.Elapsed;
        }
    }

    public async Task PauseAsync()
    {
        if (_status == DownloadStatus.Downloading)
        {
            SetStatus(DownloadStatus.Paused, "Download paused");
            _cancellationTokenSource?.Cancel();
            await Task.CompletedTask;
        }
    }

    public async Task ResumeAsync()
    {
        if (_status == DownloadStatus.Paused && _targetDirectory != null)
        {
            // Create new cancellation token and resume
            _cancellationTokenSource = new CancellationTokenSource();
            SetStatus(DownloadStatus.Downloading, "Resuming download...");
            
            // Note: Full resume implementation would require saving/loading state
            // For now, this is a simplified version
            await Task.CompletedTask;
        }
    }

    public async Task CancelAsync()
    {
        _cancellationTokenSource?.Cancel();
        SetStatus(DownloadStatus.Cancelled, "Download cancelled");
        await Task.CompletedTask;
    }

    public DownloadSummary GetDownloadSummary()
    {
        return new DownloadSummary
        {
            FinalStatus = _status,
            Statistics = _statistics,
            Errors = _errors.ToList(),
            StartTime = DateTime.Now - _statistics.ElapsedTime,
            EndTime = DateTime.Now
        };
    }

    private async Task<List<SmugMugNode>> DiscoverContentAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Starting content discovery");
        
        var rootNode = await _apiClient.GetUserRootNodeAsync(cancellationToken);
        var allNodes = await _apiClient.GetAllChildNodesRecursiveAsync(rootNode.NodeId, cancellationToken);
        
        _logger.LogInformation("Discovered {NodeCount} total nodes", allNodes.Count);
        return allNodes;
    }

    private async Task CountPhotosAsync(List<SmugMugNode> albums, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Counting photos in {AlbumCount} albums", albums.Count);
        
        var totalPhotos = 0;
        foreach (var album in albums)
        {
            try
            {
                // Extract album key from the album URI or node
                var albumKey = ExtractAlbumKey(album);
                if (albumKey != null)
                {
                    var images = await _apiClient.GetAlbumImagesAsync(albumKey, cancellationToken);
                    totalPhotos += images.Count;
                    
                    // Store images in the node for later processing
                    album.Images = images;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to count photos in album {AlbumName}", album.DisplayName);
                RecordError(ex, $"Failed to count photos in album {album.DisplayName}", album: null);
            }
        }

        _statistics.TotalPhotos = totalPhotos;
        _logger.LogInformation("Total photos to download: {PhotoCount}", totalPhotos);
        
        ReportProgress("Photo counting completed");
    }

    private async Task DownloadAllPhotosAsync(List<SmugMugNode> albums, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Starting download of photos from {AlbumCount} albums", albums.Count);

        var downloadTasks = albums.Select(album => DownloadAlbumAsync(album, cancellationToken));
        await Task.WhenAll(downloadTasks);
    }

    private async Task DownloadAlbumAsync(SmugMugNode albumNode, CancellationToken cancellationToken)
    {
        try
        {
            var albumKey = ExtractAlbumKey(albumNode);
            if (albumKey == null)
            {
                _logger.LogWarning("Could not extract album key from node {NodeId}", albumNode.NodeId);
                return;
            }

            // Create local album directory
            var albumPath = CreateAlbumPath(albumNode);
            Directory.CreateDirectory(albumPath);

            _logger.LogDebug("Processing album: {AlbumName} with {PhotoCount} photos", 
                albumNode.DisplayName, albumNode.Images.Count);

            // Download all images in the album
            var downloadTasks = albumNode.Images.Select(image => DownloadImageAsync(image, albumPath, cancellationToken));
            await Task.WhenAll(downloadTasks);

            // Update album progress
            Interlocked.Increment(ref _processedAlbums);
            ReportProgress($"Completed album: {albumNode.DisplayName}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to download album {AlbumName}", albumNode.DisplayName);
            RecordError(ex, $"Failed to download album {albumNode.DisplayName}");
        }
    }

    private async Task DownloadImageAsync(SmugMugImage image, string albumPath, CancellationToken cancellationToken)
    {
        await _downloadSemaphore.WaitAsync(cancellationToken);

        try
        {
            _logger.LogDebug("Downloading image: {ImageTitle}", image.DisplayName);

            // Get image size details to find the original/raw download URL
            var sizeDetails = await _apiClient.GetImageSizeDetailsAsync(image.ImageKey, cancellationToken);
            var originalSize = sizeDetails.GetOriginalSize();

            if (originalSize == null)
            {
                throw new InvalidOperationException($"No original size available for image {image.ImageKey}");
            }

            // Create local file path
            var fileName = image.SafeFileName;
            var filePath = Path.Combine(albumPath, fileName);

            // Skip if file already exists (resume functionality)
            if (File.Exists(filePath))
            {
                var fileInfo = new FileInfo(filePath);
                if (originalSize.Size.HasValue && fileInfo.Length == originalSize.Size.Value)
                {
                    _logger.LogDebug("Skipping existing file: {FileName}", fileName);
                    Interlocked.Increment(ref _downloadedPhotos);
                    Interlocked.Add(ref _downloadedBytes, fileInfo.Length);
                    ReportProgress($"Skipped existing: {fileName}", image);
                    return;
                }
            }

            // Download the image
            var progress = new Progress<DownloadProgress>(p =>
            {
                ReportProgress($"Downloading: {fileName} ({p.ProgressPercentage:F1}%)", image);
            });

            using var imageStream = await _apiClient.DownloadImageAsync(originalSize.Url, progress, cancellationToken);
            using var fileStream = new FileStream(filePath, FileMode.Create, FileAccess.Write);

            await imageStream.CopyToAsync(fileStream, cancellationToken);

            // Update statistics
            Interlocked.Increment(ref _downloadedPhotos);
            if (originalSize.Size.HasValue)
            {
                Interlocked.Add(ref _downloadedBytes, originalSize.Size.Value);
            }

            _logger.LogDebug("Successfully downloaded: {FileName}", fileName);
            ReportProgress($"Downloaded: {fileName}", image);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to download image {ImageKey}", image.ImageKey);
            Interlocked.Increment(ref _failedPhotos);
            RecordError(ex, $"Failed to download image {image.DisplayName}", image: image);
        }
        finally
        {
            _downloadSemaphore.Release();
        }
    }

    private string CreateAlbumPath(SmugMugNode albumNode)
    {
        if (_targetDirectory == null)
        {
            throw new InvalidOperationException("Target directory not set");
        }

        // Build path based on album hierarchy
        var pathParts = new List<string> { _targetDirectory };

        // Add parent folder names if available
        var current = albumNode.Parent;
        var folderNames = new List<string>();

        while (current != null && current.IsFolder)
        {
            folderNames.Insert(0, SanitizeFolderName(current.DisplayName));
            current = current.Parent;
        }

        pathParts.AddRange(folderNames);
        pathParts.Add(SanitizeFolderName(albumNode.DisplayName));

        return Path.Combine(pathParts.ToArray());
    }

    private static string SanitizeFolderName(string folderName)
    {
        var invalidChars = Path.GetInvalidFileNameChars();
        foreach (var invalidChar in invalidChars)
        {
            folderName = folderName.Replace(invalidChar, '_');
        }

        // Also replace some additional problematic characters
        folderName = folderName.Replace(':', '_').Replace('*', '_').Replace('?', '_');

        return folderName.Trim();
    }

    private static string? ExtractAlbumKey(SmugMugNode albumNode)
    {
        // Try to extract album key from the album URI
        if (albumNode.Uris?.Album?.Uri != null)
        {
            var uri = albumNode.Uris.Album.Uri;
            var parts = uri.Split('/');
            return parts.LastOrDefault();
        }

        // Fallback: use node ID if available
        return albumNode.NodeId;
    }

    private void SetStatus(DownloadStatus newStatus, string? message = null)
    {
        var oldStatus = _status;
        _status = newStatus;

        _logger.LogInformation("Download status changed from {OldStatus} to {NewStatus}: {Message}",
            oldStatus, newStatus, message);

        StatusChanged?.Invoke(this, new DownloadStatusChangedEventArgs(oldStatus, newStatus, message));
    }

    private void ReportProgress(string? currentOperation = null, SmugMugImage? currentImage = null)
    {
        // Update statistics with current field values
        _statistics.ProcessedAlbums = _processedAlbums;
        _statistics.DownloadedPhotos = _downloadedPhotos;
        _statistics.FailedPhotos = _failedPhotos;
        _statistics.DownloadedBytes = _downloadedBytes;
        _statistics.ElapsedTime = _stopwatch.Elapsed;

        // Calculate average download speed
        if (_statistics.ElapsedTime.TotalSeconds > 0)
        {
            _statistics.AverageDownloadSpeed = _statistics.DownloadedBytes / _statistics.ElapsedTime.TotalSeconds;
        }

        // Calculate estimated time remaining
        if (_statistics.AverageDownloadSpeed > 0 && _statistics.TotalBytes > 0)
        {
            var remainingBytes = _statistics.TotalBytes - _statistics.DownloadedBytes;
            _statistics.EstimatedTimeRemaining = TimeSpan.FromSeconds(remainingBytes / _statistics.AverageDownloadSpeed.Value);
        }

        ProgressUpdated?.Invoke(this, new DownloadProgressEventArgs(_statistics, currentOperation, currentImage));
    }

    private async Task<List<SmugMugNode>> ConvertSelectedAlbumsToNodes(List<SelectableAlbum> selectedAlbums, CancellationToken cancellationToken)
    {
        var albumNodes = new List<SmugMugNode>();

        foreach (var selectedAlbum in selectedAlbums)
        {
            try
            {
                // Create a SmugMugNode from the SelectableAlbum
                var albumNode = new SmugMugNode
                {
                    NodeId = selectedAlbum.NodeId,
                    Type = "Album",
                    Name = selectedAlbum.Name,
                    Description = selectedAlbum.Description,
                    UrlName = selectedAlbum.UrlName,
                    WebUri = selectedAlbum.WebUri,
                    Uri = $"/api/v2/album/{selectedAlbum.AlbumKey}",
                    DateAdded = selectedAlbum.DateCreated
                };

                // Get album images for this album
                var images = await _apiClient.GetAlbumImagesAsync(selectedAlbum.AlbumKey, cancellationToken);
                albumNode.Images = images;

                albumNodes.Add(albumNode);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to convert album {AlbumName} to node", selectedAlbum.DisplayName);
                RecordError(ex, $"Failed to convert album {selectedAlbum.DisplayName} to node");
            }
        }

        return albumNodes;
    }

    private void RecordError(Exception exception, string? context = null, SmugMugImage? image = null, SmugMugAlbum? album = null)
    {
        var error = new DownloadError
        {
            Message = exception.Message,
            Context = context,
            ImageKey = image?.ImageKey,
            ImageTitle = image?.DisplayName,
            AlbumKey = album?.AlbumKey,
            AlbumName = album?.DisplayName,
            Timestamp = DateTime.UtcNow,
            Exception = exception
        };

        _errors.Add(error);
        ErrorOccurred?.Invoke(this, new DownloadErrorEventArgs(exception, context, image, album));
    }

    public void Dispose()
    {
        _cancellationTokenSource?.Dispose();
        _downloadSemaphore?.Dispose();
    }
}
