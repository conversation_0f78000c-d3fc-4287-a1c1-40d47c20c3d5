using System.Windows;
using Winmug.ViewModels;
using Winmug.Core.Models;

namespace Winmug.Views;

/// <summary>
/// Interaction logic for AlbumSelectionWindow.xaml
/// </summary>
public partial class AlbumSelectionWindow : Window
{
    public AlbumSelectionViewModel ViewModel { get; }
    public List<SelectableAlbum> SelectedAlbums { get; private set; } = new();

    public AlbumSelectionWindow(AlbumSelectionViewModel viewModel)
    {
        InitializeComponent();
        ViewModel = viewModel;
        DataContext = viewModel;
        
        // Load albums when window is loaded
        Loaded += AlbumSelectionWindow_Loaded;
    }

    private async void AlbumSelectionWindow_Loaded(object sender, RoutedEventArgs e)
    {
        // Automatically load albums when window opens
        await ViewModel.LoadAlbumsCommand.ExecuteAsync(null);
    }

    private void DoneButton_Click(object sender, RoutedEventArgs e)
    {
        // Get selected albums
        SelectedAlbums = ViewModel.GetSelectedAlbums();
        
        // Set dialog result and close
        DialogResult = true;
        Close();
    }
}
