using System.Text.Json.Serialization;

namespace Winmug.Core.Models;

/// <summary>
/// Represents the size details for a SmugMug image, containing download URLs for different sizes
/// </summary>
public class SmugMugImageSizes
{
    [JsonPropertyName("ImageKey")]
    public string ImageKey { get; set; } = string.Empty;

    [JsonPropertyName("Uri")]
    public string Uri { get; set; } = string.Empty;

    [JsonPropertyName("ImageSizes")]
    public Dictionary<string, SmugMugImageSize> ImageSizes { get; set; } = new();

    // Helper methods to get specific sizes
    public SmugMugImageSize? GetOriginalSize()
    {
        return ImageSizes.Values.FirstOrDefault(s => s.IsOriginal) ??
               ImageSizes.GetValueOrDefault("OriginalSize") ??
               ImageSizes.GetValueOrDefault("5KSize") ??
               ImageSizes.GetValueOrDefault("4KSize") ??
               ImageSizes.GetValueOrDefault("3KSize") ??
               ImageSizes.Values.OrderByDescending(s => s.Width * s.Height).FirstOrDefault();
    }

    public SmugMugImageSize? GetLargestAvailableSize()
    {
        return ImageSizes.Values.OrderByDescending(s => s.Width * s.Height).FirstOrDefault();
    }
}

/// <summary>
/// Represents a specific size variant of a SmugMug image
/// </summary>
public class SmugMugImageSize
{
    [JsonPropertyName("Url")]
    public string Url { get; set; } = string.Empty;

    [JsonPropertyName("Width")]
    public int Width { get; set; }

    [JsonPropertyName("Height")]
    public int Height { get; set; }

    [JsonPropertyName("Watermark")]
    public bool? Watermark { get; set; }

    [JsonPropertyName("MD5")]
    public string? MD5 { get; set; }

    [JsonPropertyName("Size")]
    public long? Size { get; set; }

    // Helper properties
    public bool IsOriginal => Url.Contains("_O.") || Url.Contains("/O/");
    public long PixelCount => (long)Width * Height;
    public string SizeDescription => $"{Width}x{Height}";
}

/// <summary>
/// Response wrapper for SmugMug API responses
/// </summary>
public class SmugMugApiResponse<T>
{
    [JsonPropertyName("Response")]
    public T? Response { get; set; }

    [JsonPropertyName("Code")]
    public int Code { get; set; }

    [JsonPropertyName("Message")]
    public string Message { get; set; } = string.Empty;

    [JsonPropertyName("Locator")]
    public string? Locator { get; set; }

    [JsonPropertyName("LocatorType")]
    public string? LocatorType { get; set; }

    [JsonPropertyName("Uri")]
    public string? Uri { get; set; }

    [JsonPropertyName("UriDescription")]
    public string? UriDescription { get; set; }
}

/// <summary>
/// Generic response wrapper for collections
/// </summary>
public class SmugMugCollectionResponse<T>
{
    [JsonPropertyName("Uri")]
    public string Uri { get; set; } = string.Empty;

    [JsonPropertyName("Locator")]
    public string? Locator { get; set; }

    [JsonPropertyName("UriDescription")]
    public string? UriDescription { get; set; }

    [JsonPropertyName("Pages")]
    public SmugMugPagingInfo? Pages { get; set; }

    [JsonPropertyName("User")]
    public SmugMugUser? User { get; set; }

    [JsonPropertyName("Node")]
    public SmugMugNode? Node { get; set; }

    [JsonPropertyName("Album")]
    public SmugMugAlbum? Album { get; set; }

    [JsonPropertyName("Image")]
    public SmugMugImage? Image { get; set; }

    [JsonPropertyName("ImageSizes")]
    public SmugMugImageSizes? ImageSizes { get; set; }

    // Collection properties
    public List<T> Items { get; set; } = new();
}

/// <summary>
/// Paging information for SmugMug API responses
/// </summary>
public class SmugMugPagingInfo
{
    [JsonPropertyName("Total")]
    public int Total { get; set; }

    [JsonPropertyName("Start")]
    public int Start { get; set; }

    [JsonPropertyName("Count")]
    public int Count { get; set; }

    [JsonPropertyName("RequestedCount")]
    public int RequestedCount { get; set; }

    [JsonPropertyName("FirstPage")]
    public string? FirstPage { get; set; }

    [JsonPropertyName("LastPage")]
    public string? LastPage { get; set; }

    [JsonPropertyName("NextPage")]
    public string? NextPage { get; set; }

    [JsonPropertyName("PrevPage")]
    public string? PrevPage { get; set; }

    public bool HasNextPage => !string.IsNullOrEmpty(NextPage);
    public bool HasPrevPage => !string.IsNullOrEmpty(PrevPage);
}
