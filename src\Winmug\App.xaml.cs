using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System.Windows;
using Winmug.Core.Authentication;
using Winmug.Core.Services;
using Winmug.ViewModels;
using Winmug.Views;

namespace Winmug;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : Application
{
    private IHost? _host;

    protected override async void OnStartup(StartupEventArgs e)
    {
        // Set up global exception handlers
        AppDomain.CurrentDomain.UnhandledException += OnUnhandledException;
        DispatcherUnhandledException += OnDispatcherUnhandledException;

        try
        {
            base.OnStartup(e);

            // Build configuration
            var baseDirectory = AppDomain.CurrentDomain.BaseDirectory;
            var configuration = new ConfigurationBuilder()
                .SetBasePath(baseDirectory)
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .AddJsonFile("appsettings.local.json", optional: true, reloadOnChange: true)
                .Build();

            // Verify configuration is loaded
            var consumerKey = configuration["SmugMugOAuth:ConsumerKey"];
            if (string.IsNullOrEmpty(consumerKey) || consumerKey == "YOUR_SMUGMUG_API_KEY_HERE")
            {
                MessageBox.Show("SmugMug API credentials not configured!\n\nPlease update appsettings.json with your actual SmugMug API key and secret.",
                    "Configuration Error", MessageBoxButton.OK, MessageBoxImage.Warning);
            }

            // Build host
            _host = Host.CreateDefaultBuilder()
                .ConfigureServices((context, services) =>
                {
                    ConfigureServices(services, configuration);
                })
                .ConfigureLogging(logging =>
                {
                    logging.ClearProviders();
                    logging.AddConsole();
                    logging.AddDebug();
                    logging.SetMinimumLevel(LogLevel.Information);
                })
                .Build();

            await _host.StartAsync();

            // Create and show main window
            try
            {
                var mainWindow = _host.Services.GetRequiredService<MainWindow>();
                mainWindow.Show();
            }
            catch (Exception ex)
            {
                // If main window fails, show a simple test window
                MessageBox.Show($"Main window failed to load: {ex.Message}\n\nShowing test window instead.",
                    "Main Window Error", MessageBoxButton.OK, MessageBoxImage.Warning);

                var testWindow = new TestWindow();
                testWindow.Show();
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Application startup failed: {ex.Message}\n\nDetails: {ex}",
                "Startup Error", MessageBoxButton.OK, MessageBoxImage.Error);
            Shutdown(1);
        }
    }

    private void OnUnhandledException(object sender, UnhandledExceptionEventArgs e)
    {
        var exception = e.ExceptionObject as Exception;
        MessageBox.Show($"Unhandled exception: {exception?.Message}\n\nDetails: {exception}",
            "Unhandled Exception", MessageBoxButton.OK, MessageBoxImage.Error);
    }

    private void OnDispatcherUnhandledException(object sender, System.Windows.Threading.DispatcherUnhandledExceptionEventArgs e)
    {
        MessageBox.Show($"UI thread exception: {e.Exception.Message}\n\nDetails: {e.Exception}",
            "UI Exception", MessageBoxButton.OK, MessageBoxImage.Error);
        e.Handled = true; // Prevent application from crashing
    }

    protected override async void OnExit(ExitEventArgs e)
    {
        if (_host != null)
        {
            await _host.StopAsync();
            _host.Dispose();
        }
        base.OnExit(e);
    }

    private static void ConfigureServices(IServiceCollection services, IConfiguration configuration)
    {
        // Configuration
        services.Configure<SmugMugOAuthOptions>(configuration.GetSection(SmugMugOAuthOptions.SectionName));

        // HTTP Client
        services.AddHttpClient<ISmugMugApiClient, ImprovedSmugMugApiClient>(client =>
        {
            client.DefaultRequestHeaders.Add("User-Agent", "Winmug/1.0");
            client.Timeout = TimeSpan.FromMinutes(5);
        });

        // Core services
        services.AddSingleton<ISecureCredentialStorage, WindowsCredentialStorage>();
        services.AddSingleton<ISmugMugAuthenticationService, SmugMugAuthenticationService>();
        services.AddTransient<ISmugMugApiClient, ImprovedSmugMugApiClient>();
        services.AddTransient<IDownloadManager, DownloadManager>();

        // ViewModels
        services.AddTransient<MainWindowViewModel>();
        services.AddTransient<AlbumSelectionViewModel>();

        // Views
        services.AddTransient<MainWindow>();
    }
}
