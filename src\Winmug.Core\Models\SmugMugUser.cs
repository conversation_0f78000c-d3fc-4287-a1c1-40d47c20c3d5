using System.Text.Json.Serialization;

namespace Winmug.Core.Models;

/// <summary>
/// Represents a SmugMug user account with comprehensive information from /api/v2!authuser
/// </summary>
public class SmugMugUser
{
    [JsonPropertyName("Name")]
    public string Name { get; set; } = string.Empty;

    [JsonPropertyName("NickName")]
    public string NickName { get; set; } = string.Empty;

    [JsonPropertyName("DisplayName")]
    public string? DisplayName { get; set; }

    [JsonPropertyName("UserName")]
    public string? UserName { get; set; }

    [JsonPropertyName("FirstName")]
    public string? FirstName { get; set; }

    [JsonPropertyName("LastName")]
    public string? LastName { get; set; }

    [JsonPropertyName("Email")]
    public string? Email { get; set; }

    [JsonPropertyName("Plan")]
    public string? Plan { get; set; }

    [JsonPropertyName("AccountStatus")]
    public string? AccountStatus { get; set; }

    [JsonPropertyName("ImageCount")]
    public int? ImageCount { get; set; }

    [JsonPropertyName("ViewPassHint")]
    public string? ViewPassHint { get; set; }

    [JsonPropertyName("Domain")]
    public string? Domain { get; set; }

    [JsonPropertyName("RefTag")]
    public string? RefTag { get; set; }

    [JsonPropertyName("QuickShare")]
    public bool? QuickShare { get; set; }

    [JsonPropertyName("SortBy")]
    public string? SortBy { get; set; }

    [JsonPropertyName("TimeZone")]
    public string? TimeZone { get; set; }

    [JsonPropertyName("WebUri")]
    public string WebUri { get; set; } = string.Empty;

    [JsonPropertyName("Uri")]
    public string Uri { get; set; } = string.Empty;

    [JsonPropertyName("Uris")]
    public SmugMugUris? Uris { get; set; }

    /// <summary>
    /// Expanded Node data when using _expand=Node parameter
    /// This is available when calling /api/v2/user/!authuser?_expand=Node
    /// </summary>
    [JsonPropertyName("Node")]
    public SmugMugNode? Node { get; set; }

    // Helper property to get the best available name
    public string EffectiveName =>
        !string.IsNullOrEmpty(Name) ? Name :
        !string.IsNullOrEmpty(DisplayName) ? DisplayName :
        !string.IsNullOrEmpty(UserName) ? UserName : "Unknown";

    // Helper property to get the best available nickname
    public string EffectiveNickName =>
        !string.IsNullOrEmpty(NickName) ? NickName : "Unknown";

    // Helper property to extract Node ID from Node URI
    public string? NodeId
    {
        get
        {
            if (Uris?.Node?.Uri != null)
            {
                // Extract node ID from URI like "/api/v2/node/KssX2d" -> "KssX2d"
                var parts = Uris.Node.Uri.Split('/');
                return parts.Length > 0 ? parts[^1] : null;
            }
            return null;
        }
    }
}

/// <summary>
/// Contains URIs for related SmugMug resources from /api/v2!authuser response
/// </summary>
public class SmugMugUris
{
    [JsonPropertyName("Node")]
    public SmugMugUriInfo? Node { get; set; }

    [JsonPropertyName("Folder")]
    public SmugMugUriInfo? Folder { get; set; }

    [JsonPropertyName("UserProfile")]
    public SmugMugUriInfo? UserProfile { get; set; }

    [JsonPropertyName("Features")]
    public SmugMugUriInfo? Features { get; set; }

    [JsonPropertyName("User")]
    public SmugMugUriInfo? User { get; set; }

    [JsonPropertyName("UserAlbums")]
    public SmugMugUriInfo? UserAlbums { get; set; }

    [JsonPropertyName("UserRecentImages")]
    public SmugMugUriInfo? UserRecentImages { get; set; }

    [JsonPropertyName("UserFeaturedAlbums")]
    public SmugMugUriInfo? UserFeaturedAlbums { get; set; }

    [JsonPropertyName("UserPopularMedia")]
    public SmugMugUriInfo? UserPopularMedia { get; set; }

    [JsonPropertyName("UserGeoMedia")]
    public SmugMugUriInfo? UserGeoMedia { get; set; }

    [JsonPropertyName("UserImageSearch")]
    public SmugMugUriInfo? UserImageSearch { get; set; }

    [JsonPropertyName("UrlPathLookup")]
    public SmugMugUriInfo? UrlPathLookup { get; set; }

    [JsonPropertyName("BioImage")]
    public SmugMugUriInfo? BioImage { get; set; }

    [JsonPropertyName("CoverImage")]
    public SmugMugUriInfo? CoverImage { get; set; }

    [JsonPropertyName("SiteSettings")]
    public SmugMugUriInfo? SiteSettings { get; set; }

    [JsonPropertyName("UserTasks")]
    public SmugMugUriInfo? UserTasks { get; set; }

    [JsonPropertyName("UserWatermarks")]
    public SmugMugUriInfo? UserWatermarks { get; set; }

    [JsonPropertyName("UserPrintmarks")]
    public SmugMugUriInfo? UserPrintmarks { get; set; }
}

/// <summary>
/// Contains URI information for a SmugMug resource
/// </summary>
public class SmugMugUriInfo
{
    [JsonPropertyName("Uri")]
    public string Uri { get; set; } = string.Empty;

    [JsonPropertyName("Locator")]
    public string? Locator { get; set; }

    [JsonPropertyName("UriDescription")]
    public string? UriDescription { get; set; }
}

/// <summary>
/// Special response model for /api/v2!authuser endpoint where user data is nested under Response.User
/// </summary>
public class SmugMugAuthUserResponse
{
    [JsonPropertyName("User")]
    public SmugMugUser? User { get; set; }

    [JsonPropertyName("Uri")]
    public string? Uri { get; set; }

    [JsonPropertyName("Locator")]
    public string? Locator { get; set; }

    [JsonPropertyName("LocatorType")]
    public string? LocatorType { get; set; }

    [JsonPropertyName("UriDescription")]
    public string? UriDescription { get; set; }

    [JsonPropertyName("EndpointType")]
    public string? EndpointType { get; set; }

    [JsonPropertyName("ResponseLevel")]
    public string? ResponseLevel { get; set; }
}

/// <summary>
/// Represents a SmugMug user profile with additional profile information
/// </summary>
public class SmugMugUserProfile
{
    [JsonPropertyName("BioText")]
    public string? BioText { get; set; }

    [JsonPropertyName("FirstName")]
    public string? FirstName { get; set; }

    [JsonPropertyName("LastName")]
    public string? LastName { get; set; }

    [JsonPropertyName("DisplayName")]
    public string? DisplayName { get; set; }

    [JsonPropertyName("ContactEmail")]
    public string? ContactEmail { get; set; }

    [JsonPropertyName("ValidEmail")]
    public bool? ValidEmail { get; set; }

    [JsonPropertyName("Uri")]
    public string Uri { get; set; } = string.Empty;

    [JsonPropertyName("Uris")]
    public SmugMugUserProfileUris? Uris { get; set; }
}

/// <summary>
/// Contains URIs for SmugMug user profile related resources
/// </summary>
public class SmugMugUserProfileUris
{
    [JsonPropertyName("User")]
    public SmugMugUriInfo? User { get; set; }

    [JsonPropertyName("BioImage")]
    public SmugMugUriInfo? BioImage { get; set; }

    [JsonPropertyName("CoverImage")]
    public SmugMugUriInfo? CoverImage { get; set; }
}
