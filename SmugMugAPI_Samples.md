SmugMug API v2: Implementation Guide
1. Introduction & Core Concepts
The SmugMug v2 API is a comprehensive, RESTful API that allows developers to interact with nearly every aspect of the SmugMug platform. It uses JSON for all data interchange and is built on a hierarchical structure.
Key Principles:
RESTful: Uses standard HTTP verbs (GET, POST, PATCH, DELETE).
JSON-based: All requests and responses use application/json.
HATEOAS (Hypermedia as the Engine of Application State): Responses contain URIs for related resources, allowing you to navigate the API without hardcoding URLs.
Hierarchical Structure: All content is organized within a tree of Nodes.
Core Data Objects:
User: Represents a SmugMug account. Every action is performed in the context of a user.
Node: The most fundamental concept. A Node is a container in the SmugMug hierarchy. It can represent:
A Folder: Contains other Folders or Albums.
An Album: Contains Images and Videos.
System Pages (like Homepage, About, etc.).
Album: A collection of images and videos. An Album is a specific type of Node.
Image/Video: The actual media content within an Album.
2. Authentication: OAuth 1.0a
The API is secured using the OAuth 1.0a protocol. You cannot use a simple API key for requests; you must complete the full 3-legged authentication flow to obtain an Access Token for a specific user.
Prerequisites:
You must apply for an API Key (Consumer Key and Consumer Secret) from SmugMug.
Authentication Flow:
This is a standard 3-legged process:
Step 1: Obtain a Request Token
Endpoint: https://secure.smugmug.com/services/oauth/1.0a/getRequestToken
Method: GET
Parameters:
oauth_callback: The URL you want SmugMug to redirect the user back to after authorization.
oauth_consumer_key: Your API key.
(Other standard oauth_ parameters like nonce, timestamp, signature, etc.)
Response: An oauth_token (Request Token) and oauth_token_secret.
Step 2: Authorize the Request Token
Redirect the user's browser to the authorization URL:
Endpoint: https://secure.smugmug.com/services/oauth/1.0a/authorize
Parameters:
oauth_token: The Request Token from Step 1.
Access: (Optional) Full, Read-Only.
Permissions: (Optional) Add-Only.
The user will log in and grant your application permission. They will then be redirected to your oauth_callback URL with the oauth_token and a new oauth_verifier in the query string.
Step 3: Exchange for an Access Token
Endpoint: https://secure.smugmug.com/services/oauth/1.0a/getAccessToken
Method: GET
Parameters:
oauth_consumer_key: Your API key.
oauth_token: The Request Token from Step 1.
oauth_verifier: The verifier from the callback in Step 2.
(Other standard oauth_ parameters, signed with the Request Token Secret).
Response: A new, long-lived oauth_token (the Access Token) and oauth_token_secret (the Access Token Secret).
Implementation Note: Securely store the final Access Token and Access Token Secret for the user. All subsequent API calls must be signed with these credentials.
3. Making API Requests
Base URL
All API requests are made to: https://api.smugmug.com
Headers
Accept: application/json: This header is mandatory for all requests.
Authorization: Contains the OAuth 1.0a signature and parameters.
HTTP Verbs
GET: Retrieve data. Parameters are passed in the query string.
POST: Create a new resource. Data is passed as a JSON object in the request body.
PATCH: Update an existing resource. Data is passed as a JSON object containing only the fields to be changed.
DELETE: Remove a resource.
"Bang URIs" (!)"
SmugMug uses a ! prefix as a shortcut for the currently authenticated object. This is extremely useful.
!authuser: Represents the currently authenticated user. GET /api/v2/user/!authuser retrieves the user's profile.
!UPLOAD: A special URI for uploading files (see Uploading section).
4. Understanding Responses
All successful responses (HTTP 200 OK, 201 Created, 202 Accepted) follow a standard envelope format.
{
  "Response": {
    "Uri": "/api/v2/user/ABCDE",
    "Locator": "User",
    "User": {
      "Name": "John Smith",
      "NickName": "jsmith",
      // ... other user fields
    },
    "Uris": {
      "Node": {
        "Uri": "/api/v2/node/XqZ8f"
      },
      "Folder": {
        "Uri": "/api/v2/folder/user/jsmith"
      },
      // ... other related URIs
    }
  },
  "Code": 200,
  "Message": "Ok"
}
Use code with caution.
Json
Response: The main wrapper containing the data.
Response.<Locator>: The primary data object (e.g., Response.User, Response.Album).
Response.Uris: A crucial HATEOAS object containing links to related resources. Always use these URIs for navigation instead of building URLs manually.
Code / Message: The HTTP status code and a human-readable message.
Pagination
For endpoints that return a list of items (e.g., images in an album), use the _start and _count query parameters to paginate.
GET /api/v2/album/XXXXX!images?_start=1&_count=50

6. Core API Endpoint Reference
This is a summary of the most common endpoints and their primary functions.
6.1. User & Account
GET /api/v2/user/!authuser: Get the profile of the currently logged-in user.
point for navigating their content.
6.2. Content Organization (Nodes, Folders, Albums)
Node URIs: A node is identified by a unique NodeID, e.g., /api/v2/node/XqZ8f.
GET /api/v2/node/XXXXX: Get details for a specific Node.
PATCH /api/v2/node/XXXXX: Update node properties (e.g., Name, Description).
POST /api/v2/folder/user/NICKNAME!albums: Create a new album inside a user's root folder.
POST /api/v2/node/XXXXX!createnode: A flexible way to create a child node (Folder or Album) inside an existing node.
DELETE /api/v2/node/XXXXX: Delete a node (and all its contents). This is permanent.
6.3. Content Management (Images & Videos)
GET /api/v2/album/XXXXX!images: Get a list of images in an album.
GET /api/v2/image/XXXXX-S: Get details for a specific image.
PATCH /api/v2/image/XXXXX-S: Update image metadata (caption, keywords, etc.).
DELETE /api/v2/image/XXXXX-S: Delete an image.
6.4. Uploading Files
Uploading is a multi-step process. You cannot POST the binary file directly to an album.
Step 1: Send a POST Request to the Upload Endpoint
Endpoint: https://api.smugmug.com/api/v2/album/XXXXX!images (use the !images sub-resource URI from the target album).
Method: POST
Headers:
X-Smug-Version: v2
X-Smug-FileName: my-photo.jpg
X-Smug-Caption: A beautiful sunset. (optional)
X-Smug-Keywords: travel; beach (optional)
X-Smug-MD5: <MD5_checksum_of_the_file> (optional but recommended)
And your standard Authorization and Accept headers.
Body: The request body should be empty.
Response: You will get a 202 Accepted response. The Response.Image.AlbumImageUri gives you the URI of the image placeholder, and most importantly, the Response.Image.UploadURL gives you the temporary upload location.
Step 2: PUT the File Binary to the Upload URL
Endpoint: The UploadURL from the previous step.
Method: PUT
Headers: Content-Length: <size_of_file_in_bytes>
Body: The raw binary data of your image/video file.
7. Implementation Walkthrough: Upload a Photo to a New Album
Here is a step-by-step workflow for a common task.
Get User's Root Node:
GET /api/v2/user/!authuser?_expand=Node
From the response, save the Response.User.Uris.Node.Uri. Let's call it rootNodeUri.
Create a New Album:
An album is a type of Node. We will create it inside the user's root node.
POST <rootNodeUri>!createnode
Request Body:
{
  "Type": "Album",
  "Name": "My Awesome Trip",
  "UrlName": "My-Awesome-Trip"
}
Use code with caution.
Json
From the response, save the Response.Node.Uris.Album.Uri. Let's call it newAlbumUri.
Prepare for Upload:
Get the !images URI from the album you just created. From the POST response in step 2, find Response.Node.Uris.AlbumImages.Uri. Let's call it albumImagesUri.
Calculate the MD5 checksum of your local image file (photo.jpg).
Initiate Upload:
POST <albumImagesUri>
Headers:
X-Smug-FileName: photo.jpg
X-Smug-MD5: <your_md5_checksum>
(OAuth headers)
From the 202 Accepted response, get the Response.Image.UploadURL.
Perform Upload:
PUT <UploadURL_from_step_4>
Body: The raw binary data of photo.jpg.
(Optional) Verify Upload:
Wait a few moments for processing.
GET <newAlbumUri>?_expand=AlbumImages
Check the AlbumImages array in the response to confirm your image is listed.
8. Error Handling & Rate Limiting
Error Responses: Failed requests will return a standard HTTP error code (e.g., 401, 403, 404, 429) and a JSON body with a Code and Message explaining the issue.
Rate Limiting: The API is rate-limited. While specific limits are not published, you should monitor the response headers for X-RateLimit- headers and implement a backoff strategy if you receive 429 Too Many Requests errors.


Getting the user's complete folder structure is a fundamental task, and it's a perfect example of how to navigate the SmugMug API's hierarchical and HATEOAS-based design.
You cannot get the entire folder tree in a single API call. Instead, you must traverse the hierarchy recursively, starting from the user's root and fetching the children of each folder.
Here is a detailed guide on how to implement this.
The Core Concept: Recursive Traversal of Nodes
Everything is a Node: In SmugMug, Folders and Albums are both types of Nodes. A folder is a node that can contain other nodes. An album is a node that contains images.
Start at the Root: You begin by finding the user's top-level (root) Node.
Fetch Children: For any given Node, you request its list of !children.
Recurse: For each child that is a Folder, you repeat the process: fetch its children. You continue this process until you have explored all branches of the folder tree.
Step-by-Step Implementation Guide
Step 1: Find the User's Root Node (The Starting Point)
This is the most important first step. You need to get the NodeID of the user's top-level folder, which is where their entire organizational structure begins.
API Call: Make a GET request to the authenticated user endpoint and expand the Node object.
Endpoint: /api/v2/user/!authuser?_expand=Node
Example Response (abbreviated):
{
  "Response": {
    "User": {
      "NickName": "jsmith",
      "Uris": {
        "Node": {
          "Uri": "/api/v2/node/XqZ8f" // The URI of the root node
        }
      },
      "Node": { // The expanded Node object
        "Name": "jsmith",
        "Type": "Folder",
        "NodeID": "XqZ8f", // <-- THIS IS THE ID YOU NEED TO START
        "Uris": {
          "ChildNodes": {
            "Uri": "/api/v2/node/XqZ8f!children" // The URI to get its children
          }
        }
      }
    }
  },
  "Code": 200,
  "Message": "Ok"
}
Use code with caution.
Json
From this response, you have your starting NodeID: XqZ8f.
Step 2: Get the Children of a Node
Now that you have a NodeID, you can get its contents (the folders and albums directly inside it).
API Call: Make a GET request to the !children sub-resource of the Node.
Endpoint: /api/v2/node/XqZ8f!children
Optimization Tip: Always use the _expand parameter here to get the full details of the child nodes in one go. This avoids having to make a separate API call for each child to get its name and type.
Optimized Endpoint: /api/v2/node/XqZ8f!children?_expand=ChildNodes
Example Response:
The response will contain a Node object which is an array of all the child nodes.
{
  "Response": {
    "Node": [ // An array of child nodes
      {
        "Name": "Travel Photos",
        "Type": "Folder", // <-- This is a folder, we need to recurse into it
        "NodeID": "ABCd1",
        "Uris": {
          "ChildNodes": {
            "Uri": "/api/v2/node/ABCd1!children"
          }
        }
      },
      {
        "Name": "Family Events",
        "Type": "Folder", // <-- Also a folder
        "NodeID": "EFGh2",
        "Uris": {
          "ChildNodes": {
            "Uri": "/api/v2/node/EFGh2!children"
          }
        }
      },
      {
        "Name": "Unsorted Pictures",
        "Type": "Album", // <-- This is an album, we stop here for this branch
        "NodeID": "IJKl3",
        "Uris": {
          "AlbumImages": {
            "Uri": "/api/v2/album/IJKl3!images"
          }
        }
      }
    ]
  },
  "Code": 200,
  "Message": "Ok"
}
Use code with caution.
Json
Step 3: The Recursive Logic (Putting it all Together)
Now, you combine these steps into a function that can call itself. Here is the logic in pseudocode.
// Data structure to hold our final tree
let mySiteStructure = {};

// The main function to kick things off
function buildUserFolderTree() {
  // 1. Get the root node
  let rootNodeResponse = api.get("/api/v2/user/!authuser?_expand=Node");
  let rootNode = rootNodeResponse.Response.User.Node;

  // Initialize our tree structure with the root information
  mySiteStructure = {
    "name": rootNode.Name,
    "type": "Folder",
    "id": rootNode.NodeID,
    "children": []
  };

  // 2. Start the recursive process on the root node
  mySiteStructure.children = getChildrenForNode(rootNode.NodeID);
  
  // Now mySiteStructure contains the full tree
  print(mySiteStructure);
}

// The recursive function that does the heavy lifting
function getChildrenForNode(nodeID) {
  let childNodesList = [];

  // Get the children of the current node, expanding them for details
  let childrenResponse = api.get("/api/v2/node/" + nodeID + "!children?_expand=ChildNodes");
  let children = childrenResponse.Response.Node;

  if (children) {
    // Loop through each child found
    for each (child in children) {
      if (child.Type == "Folder") {
        // If the child is a FOLDER, create its object and then
        // RECURSIVELY call this function to get its children
        let folderNode = {
          "name": child.Name,
          "type": "Folder",
          "id": child.NodeID,
          "children": getChildrenForNode(child.NodeID) // <-- RECURSIVE CALL
        };
        childNodesList.append(folderNode);
      } 
      else if (child.Type == "Album") {
        // If the child is an ALBUM, we just add it and stop this branch
        let albumNode = {
          "name": child.Name,
          "type": "Album",
          "id": child.NodeID,
          "children": [] // Albums don't have their own folder structure
        };
        childNodesList.append(albumNode);
      }
    }
  }

  return childNodesList;
}

// Start the entire process
buildUserFolderTree();
Use code with caution.
Pseudocode
Summary of the Workflow
Create a recursive function getChildren(nodeID).
Inside the function, call GET /api/v2/node/{nodeID}!children?_expand=ChildNodes.
Iterate through the results.
If a child's Type is Folder, add it to your tree and call getChildren() on its NodeID.
If a child's Type is Album, add it to your tree and stop that branch.
Start the process by calling your recursive function with the root NodeID from Step 1.
