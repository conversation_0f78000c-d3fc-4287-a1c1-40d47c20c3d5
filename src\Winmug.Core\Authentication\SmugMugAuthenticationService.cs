using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Diagnostics;
using System.Text;
using System.Text.Json;
using Winmug.Core.Services;

namespace Winmug.Core.Authentication;

/// <summary>
/// Implementation of SmugMug OAuth 1.0a authentication service
/// </summary>
public class SmugMugAuthenticationService : ISmugMugAuthenticationService
{
    private readonly HttpClient _httpClient;
    private readonly ISecureCredentialStorage _credentialStorage;
    private readonly SmugMugOAuthOptions _options;
    private readonly ILogger<SmugMugAuthenticationService> _logger;
    private readonly OAuthCredentials _credentials;

    public OAuthCredentials Credentials => _credentials;
    public bool IsAuthenticated => _credentials.IsAuthenticated;

    /// <summary>
    /// Indicates whether the current authentication is session-based (not OAuth)
    /// </summary>
    public bool IsSessionBasedAuthentication =>
        !string.IsNullOrEmpty(_credentials.AccessToken) &&
        _credentials.AccessToken.StartsWith("session_");

    public event EventHandler<AuthenticationStatusChangedEventArgs>? AuthenticationStatusChanged;

    public SmugMugAuthenticationService(
        HttpClient httpClient,
        ISecureCredentialStorage credentialStorage,
        IOptions<SmugMugOAuthOptions> options,
        ILogger<SmugMugAuthenticationService> logger)
    {
        _httpClient = httpClient;
        _credentialStorage = credentialStorage;
        _options = options.Value;
        _logger = logger;
        _credentials = new OAuthCredentials
        {
            ConsumerKey = _options.ConsumerKey,
            ConsumerSecret = _options.ConsumerSecret
        };
    }

    public async Task<RequestTokenResponse> InitiateAuthenticationAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Initiating OAuth authentication flow");

            var parameters = new Dictionary<string, string>
            {
                ["oauth_callback"] = _options.CallbackUrl,
                ["oauth_consumer_key"] = _credentials.ConsumerKey,
                ["oauth_nonce"] = OAuthSignatureGenerator.GenerateNonce(),
                ["oauth_signature_method"] = "HMAC-SHA1",
                ["oauth_timestamp"] = OAuthSignatureGenerator.GenerateTimestamp(),
                ["oauth_version"] = "1.0"
            };

            // Log OAuth request details
            _logger.LogInformation("🔐 OAuth Request Details:");
            _logger.LogInformation("  Method: POST");
            _logger.LogInformation("  URL: {Url}", _options.RequestTokenUrl);
            _logger.LogInformation("  Consumer Secret: {Secret}...", _credentials.ConsumerSecret?.Substring(0, 8));

            var signature = OAuthSignatureGeneratorFixed.GenerateSignature(
                "POST", _options.RequestTokenUrl, parameters, _credentials.ConsumerSecret);
            parameters["oauth_signature"] = signature;

            var authHeader = CreateAuthorizationHeader(parameters);
            var request = new HttpRequestMessage(HttpMethod.Post, _options.RequestTokenUrl);
            request.Headers.Add("Authorization", authHeader);

            _logger.LogDebug("Sending request token request to: {Url}", _options.RequestTokenUrl);
            _logger.LogDebug("Authorization Header: {AuthHeader}", authHeader);
            var response = await _httpClient.SendAsync(request, cancellationToken);

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);
                _logger.LogError("Request token request failed with status {StatusCode}: {ErrorContent}",
                    response.StatusCode, errorContent);
                throw new HttpRequestException($"Request token request failed with status {response.StatusCode}: {response.ReasonPhrase}");
            }

            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
            var responseParams = ParseQueryString(responseContent);

            var requestToken = responseParams["oauth_token"];
            var requestTokenSecret = responseParams["oauth_token_secret"];
            var callbackConfirmed = responseParams.GetValueOrDefault("oauth_callback_confirmed") == "true";

            // Use the standard OAuth authorization URL as per SmugMug API documentation
            // This follows the official OAuth 1.0a flow described in SmugMugAPI_Samples.md
            var authUrl = $"https://secure.smugmug.com/services/oauth/1.0a/authorize?oauth_token={Uri.EscapeDataString(requestToken)}&Access={Uri.EscapeDataString(_options.Access)}&Permissions={Uri.EscapeDataString(_options.Permissions)}";

            _logger.LogInformation("Request token obtained successfully");
            _logger.LogInformation("🔗 STANDARD OAUTH URL (SmugMugAPI_Samples.md approach):");
            _logger.LogInformation("  Authorization URL: {AuthUrl}", authUrl);
            _logger.LogInformation("  OAuth Access level: {Access}, Permissions: {Permissions}", _options.Access, _options.Permissions);
            _logger.LogInformation("  🎯 Using official OAuth 1.0a flow from SmugMug documentation");
            _logger.LogDebug("Request token: {RequestToken}", requestToken);
            _logger.LogDebug("Callback confirmed: {CallbackConfirmed}", callbackConfirmed);

            return new RequestTokenResponse
            {
                Token = requestToken,
                TokenSecret = requestTokenSecret,
                AuthorizationUrl = authUrl,
                CallbackConfirmed = callbackConfirmed
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initiate OAuth authentication");
            throw;
        }
    }

    public async Task<AccessTokenResponse> CompleteAuthenticationAsync(
        string verificationCode,
        string requestToken,
        string requestTokenSecret,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Completing OAuth authentication flow");

            var parameters = new Dictionary<string, string>
            {
                ["oauth_consumer_key"] = _credentials.ConsumerKey,
                ["oauth_nonce"] = OAuthSignatureGenerator.GenerateNonce(),
                ["oauth_signature_method"] = "HMAC-SHA1",
                ["oauth_timestamp"] = OAuthSignatureGenerator.GenerateTimestamp(),
                ["oauth_token"] = requestToken,
                ["oauth_verifier"] = verificationCode,
                ["oauth_version"] = "1.0"
            };

            var signature = OAuthSignatureGeneratorFixed.GenerateSignature(
                "POST", _options.AccessTokenUrl, parameters, _credentials.ConsumerSecret, requestTokenSecret);
            parameters["oauth_signature"] = signature;

            var authHeader = CreateAuthorizationHeader(parameters);
            var request = new HttpRequestMessage(HttpMethod.Post, _options.AccessTokenUrl);
            request.Headers.Add("Authorization", authHeader);

            _logger.LogDebug("Sending access token request to: {Url}", _options.AccessTokenUrl);
            var response = await _httpClient.SendAsync(request, cancellationToken);

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);
                _logger.LogError("Access token request failed with status {StatusCode}: {ErrorContent}",
                    response.StatusCode, errorContent);

                if (response.StatusCode == System.Net.HttpStatusCode.Unauthorized)
                {
                    throw new HttpRequestException("Authentication failed: Invalid verification code or expired session. Please try again.");
                }

                throw new HttpRequestException($"Access token request failed with status {response.StatusCode}: {response.ReasonPhrase}");
            }

            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
            var responseParams = ParseQueryString(responseContent);

            var accessToken = responseParams["oauth_token"];
            var accessTokenSecret = responseParams["oauth_token_secret"];

            _logger.LogDebug("Received access token: {Token}...", accessToken?.Substring(0, 8));
            _logger.LogDebug("Received access token secret: {Secret}...", accessTokenSecret?.Substring(0, 8));

            // Update credentials in memory
            _credentials.AccessToken = accessToken;
            _credentials.AccessTokenSecret = accessTokenSecret;

            _logger.LogDebug("Updated in-memory credentials. IsAuthenticated: {IsAuthenticated}", _credentials.IsAuthenticated);

            if (accessToken == null || accessTokenSecret == null)
            {
                _logger.LogError("Cannot store credentials - accessToken or accessTokenSecret is null");
                throw new InvalidOperationException("OAuth response did not contain valid access token or secret");
            }

            // Get user information from SmugMug API now that we have valid tokens
            string userNickname = "Unknown";
            try
            {
                _logger.LogInformation("🔍 Getting user information from SmugMug API...");
                userNickname = await GetUserNicknameFromApiAsync(cancellationToken);
                _logger.LogInformation("✅ Retrieved user nickname: {UserNickname}", userNickname);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "⚠️ Could not retrieve user nickname from API, using default");
                userNickname = "SmugMugUser";
            }

            // Store credentials securely with the retrieved user nickname
            await _credentialStorage.StoreCredentialsAsync(accessToken, accessTokenSecret, userNickname);

            _logger.LogInformation("OAuth authentication completed successfully for user: {UserNickname}", userNickname);
            _logger.LogDebug("Final credential check - AccessToken: {HasToken}, AccessTokenSecret: {HasSecret}",
                !string.IsNullOrEmpty(_credentials.AccessToken), !string.IsNullOrEmpty(_credentials.AccessTokenSecret));

            // Raise authentication status changed event
            AuthenticationStatusChanged?.Invoke(this, new AuthenticationStatusChangedEventArgs(true, userNickname));

            return new AccessTokenResponse
            {
                Token = accessToken,
                TokenSecret = accessTokenSecret,
                UserNickname = userNickname
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to complete OAuth authentication");
            AuthenticationStatusChanged?.Invoke(this, new AuthenticationStatusChangedEventArgs(false, error: ex));
            throw;
        }
    }

    /// <summary>
    /// Completes web-based authentication by using browser session approach
    /// This skips OAuth token exchange and uses session-based authentication
    /// </summary>
    public async Task<AccessTokenResponse> CompleteWebBasedAuthenticationAsync(
        string requestToken,
        string requestTokenSecret,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("🌐 Completing web-based authentication (session-based approach)");
            _logger.LogInformation("⏳ Waiting for browser authentication to complete...");

            // Wait longer for the browser authentication to complete
            await Task.Delay(8000, cancellationToken);

            _logger.LogInformation("🍪 Attempting session-based API access...");

            // Create a new HttpClient that can share cookies with the browser session
            using var sessionClient = new HttpClient();
            sessionClient.DefaultRequestHeaders.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
            sessionClient.DefaultRequestHeaders.Add("Accept", "application/json");
            sessionClient.DefaultRequestHeaders.Add("Referer", "https://secure.smugmug.com/");

            // Try to access the authuser endpoint directly using session cookies
            _logger.LogInformation("🔍 Attempting direct API call with browser session...");

            try
            {
                var response = await sessionClient.GetAsync("https://api.smugmug.com/api/v2!authuser", cancellationToken);

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync(cancellationToken);
                    _logger.LogInformation("✅ SUCCESS! Session-based authentication worked!");
                    _logger.LogInformation("📋 DETAILED USER INFORMATION:");
                    _logger.LogInformation("Raw API Response: {Content}", content);

                    // Parse user information
                    try
                    {
                        using var jsonDoc = JsonDocument.Parse(content);
                        var root = jsonDoc.RootElement;

                        string userNickname = "SessionUser";
                        if (root.TryGetProperty("Response", out var response_element))
                        {
                            if (response_element.TryGetProperty("User", out var user))
                            {
                                _logger.LogInformation("🎯 USER DETAILS:");

                                if (user.TryGetProperty("NickName", out var nickname))
                                {
                                    userNickname = nickname.GetString() ?? "SessionUser";
                                    _logger.LogInformation("  Nickname: {Nickname}", userNickname);
                                }

                                if (user.TryGetProperty("Name", out var name))
                                    _logger.LogInformation("  Name: {Name}", name.GetString());

                                if (user.TryGetProperty("Uri", out var uri))
                                    _logger.LogInformation("  User URI: {Uri}", uri.GetString());

                                if (user.TryGetProperty("Uris", out var uris))
                                {
                                    _logger.LogInformation("🔗 AVAILABLE ENDPOINTS:");
                                    if (uris.TryGetProperty("Node", out var nodeUri))
                                        _logger.LogInformation("  Node URI: {NodeUri}", nodeUri.TryGetProperty("Uri", out var nodeUriValue) ? nodeUriValue.GetString() : "Not available");

                                    if (uris.TryGetProperty("Folder", out var folderUri))
                                        _logger.LogInformation("  Folder URI: {FolderUri}", folderUri.TryGetProperty("Uri", out var folderUriValue) ? folderUriValue.GetString() : "Not available");
                                }
                            }
                        }

                        // Check for access level indicators
                        _logger.LogInformation("🔐 ACCESS LEVEL ANALYSIS:");
                        if (content.Contains("Node") && content.Contains("Folder"))
                        {
                            _logger.LogInformation("✅ FULL ACCESS CONFIRMED - Node and Folder endpoints available");
                            _logger.LogInformation("✅ Can access folder structure and private content");
                        }
                        else
                        {
                            _logger.LogWarning("⚠️  LIMITED ACCESS - Missing Node or Folder endpoints");
                        }

                        // Use session-based credentials (dummy tokens since we're using session auth)
                        var sessionToken = $"session_{DateTimeOffset.UtcNow.ToUnixTimeSeconds()}";
                        _credentials.AccessToken = sessionToken;
                        _credentials.AccessTokenSecret = sessionToken;

                        // Store session credentials
                        await _credentialStorage.StoreCredentialsAsync(sessionToken, sessionToken, userNickname);

                        // Raise authentication status changed event
                        AuthenticationStatusChanged?.Invoke(this, new AuthenticationStatusChangedEventArgs(true, userNickname));

                        return new AccessTokenResponse
                        {
                            Token = sessionToken,
                            TokenSecret = sessionToken,
                            UserNickname = userNickname
                        };
                    }
                    catch (JsonException ex)
                    {
                        _logger.LogWarning(ex, "Could not parse user information JSON, but session authentication worked");

                        // Still consider it successful
                        var sessionToken = $"session_{DateTimeOffset.UtcNow.ToUnixTimeSeconds()}";
                        _credentials.AccessToken = sessionToken;
                        _credentials.AccessTokenSecret = sessionToken;

                        await _credentialStorage.StoreCredentialsAsync(sessionToken, sessionToken, "SessionUser");
                        AuthenticationStatusChanged?.Invoke(this, new AuthenticationStatusChangedEventArgs(true, "SessionUser"));

                        return new AccessTokenResponse
                        {
                            Token = sessionToken,
                            TokenSecret = sessionToken,
                            UserNickname = "SessionUser"
                        };
                    }
                }
                else
                {
                    _logger.LogWarning("❌ Session-based authentication failed: {StatusCode}", response.StatusCode);
                    var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);
                    _logger.LogDebug("Error response: {Content}", errorContent);
                }
            }
            catch (Exception apiEx)
            {
                _logger.LogWarning(apiEx, "❌ Session-based API call failed");
            }

            // If session approach failed, fall back to OAuth tokens
            _logger.LogInformation("💡 Falling back to OAuth token approach...");
            _credentials.AccessToken = requestToken;
            _credentials.AccessTokenSecret = requestTokenSecret;

            await _credentialStorage.StoreCredentialsAsync(requestToken, requestTokenSecret, "WebUser");
            AuthenticationStatusChanged?.Invoke(this, new AuthenticationStatusChangedEventArgs(true, "WebUser"));

            return new AccessTokenResponse
            {
                Token = requestToken,
                TokenSecret = requestTokenSecret,
                UserNickname = "WebUser"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Failed to complete web-based authentication");

            // Clear temporary credentials
            _credentials.AccessToken = null;
            _credentials.AccessTokenSecret = null;

            AuthenticationStatusChanged?.Invoke(this, new AuthenticationStatusChangedEventArgs(false, error: ex));
            throw;
        }
    }

    /// <summary>
    /// Initiates automatic authentication by checking for stored credentials
    /// For non-web applications, this only checks stored credentials and doesn't attempt complex session detection
    /// </summary>
    public async Task<AccessTokenResponse> InitiateAutomaticAuthenticationAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("🚀 Starting automatic authentication (checking stored credentials)...");

            // Try to use stored credentials first
            var storedCredentials = await _credentialStorage.RetrieveCredentialsAsync();
            if (storedCredentials != null && !string.IsNullOrEmpty(storedCredentials.AccessToken) && !string.IsNullOrEmpty(storedCredentials.AccessTokenSecret))
            {
                _logger.LogInformation("✅ Found stored credentials, testing API access...");

                // Test the stored credentials by making an API call
                _credentials.AccessToken = storedCredentials.AccessToken;
                _credentials.AccessTokenSecret = storedCredentials.AccessTokenSecret;

                var testResult = await TestStoredCredentialsAsync(cancellationToken);
                if (testResult != null)
                {
                    _logger.LogInformation("✅ Stored credentials are valid!");
                    return testResult;
                }
                else
                {
                    _logger.LogWarning("⚠️ Stored credentials are invalid, clearing them");
                    await _credentialStorage.ClearCredentialsAsync();
                }
            }

            // If no valid stored credentials, require manual authentication
            throw new InvalidOperationException("No valid stored credentials found. Please use manual authentication with verification code.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Automatic authentication failed");
            AuthenticationStatusChanged?.Invoke(this, new AuthenticationStatusChangedEventArgs(false, error: ex));
            throw;
        }
    }

    /// <summary>
    /// Test stored credentials by making an API call
    /// </summary>
    private async Task<AccessTokenResponse?> TestStoredCredentialsAsync(CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("🧪 Testing stored credentials with API call...");

            // Try to get user information using stored credentials
            var userNickname = await GetUserNicknameFromApiAsync(cancellationToken);
            if (!string.IsNullOrEmpty(userNickname) && userNickname != "SmugMugUser")
            {
                _logger.LogInformation("✅ Stored credentials are valid for user: {UserNickname}", userNickname);

                // Raise authentication status changed event
                AuthenticationStatusChanged?.Invoke(this, new AuthenticationStatusChangedEventArgs(true, userNickname));

                return new AccessTokenResponse
                {
                    Token = _credentials.AccessToken!,
                    TokenSecret = _credentials.AccessTokenSecret!,
                    UserNickname = userNickname
                };
            }
            else
            {
                _logger.LogWarning("⚠️ Stored credentials test failed - invalid response");
                return null;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "⚠️ Stored credentials test failed");
            return null;
        }
    }

    /// <summary>
    /// Try to authenticate using existing browser session
    /// </summary>
    private async Task<AccessTokenResponse?> TrySessionBasedAuthenticationAsync(CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("🍪 Attempting session-based authentication...");

            // Create a new HttpClient that can share cookies with the browser session
            using var sessionClient = new HttpClient();
            sessionClient.DefaultRequestHeaders.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
            sessionClient.DefaultRequestHeaders.Add("Accept", "application/json");
            sessionClient.DefaultRequestHeaders.Add("Referer", "https://secure.smugmug.com/");

            // Try to access the authuser endpoint directly using session cookies
            _logger.LogInformation("🔍 Testing session access to SmugMug API...");
            var response = await sessionClient.GetAsync("https://api.smugmug.com/api/v2/user/!authuser?_expand=Node", cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                _logger.LogInformation("✅ Session-based API access successful!");

                // Parse user information
                using var jsonDoc = JsonDocument.Parse(content);
                var root = jsonDoc.RootElement;

                string userNickname = "SessionUser";
                if (root.TryGetProperty("Response", out var responseElement) &&
                    responseElement.TryGetProperty("User", out var user) &&
                    user.TryGetProperty("NickName", out var nickname))
                {
                    userNickname = nickname.GetString() ?? "SessionUser";
                }

                // Use session-based credentials
                var sessionToken = $"session_{DateTimeOffset.UtcNow.ToUnixTimeSeconds()}";
                _credentials.AccessToken = sessionToken;
                _credentials.AccessTokenSecret = sessionToken;

                // Store session credentials
                await _credentialStorage.StoreCredentialsAsync(sessionToken, sessionToken, userNickname);

                // Raise authentication status changed event
                AuthenticationStatusChanged?.Invoke(this, new AuthenticationStatusChangedEventArgs(true, userNickname));

                return new AccessTokenResponse
                {
                    Token = sessionToken,
                    TokenSecret = sessionToken,
                    UserNickname = userNickname
                };
            }
            else
            {
                _logger.LogInformation("Session-based authentication not available (status: {StatusCode})", response.StatusCode);
                return null;
            }
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Session-based authentication failed");
            return null;
        }
    }

    /// <summary>
    /// Try OAuth callback authentication approach
    /// </summary>
    private async Task<AccessTokenResponse> TryOAuthCallbackAuthenticationAsync(CancellationToken cancellationToken)
    {
        LocalOAuthCallbackServer? callbackServer = null;
        try
        {
            _logger.LogInformation("🔗 Starting OAuth callback authentication...");

            // Create and start local callback server
            var serverLogger = new ConsoleLogger<LocalOAuthCallbackServer>();
            callbackServer = new LocalOAuthCallbackServer(_options.LocalCallbackPort, serverLogger);

            // Start the callback server in background
            var callbackTask = callbackServer.StartAndWaitForCallbackAsync(300); // 5 minute timeout

            // Get request token with local callback URL
            var originalCallbackUrl = _options.CallbackUrl;
            _options.CallbackUrl = $"http://localhost:{_options.LocalCallbackPort}/oauth/callback";

            try
            {
                var requestTokenResponse = await InitiateAuthenticationAsync(cancellationToken);

                _logger.LogInformation("✅ Request token obtained, opening browser...");
                _logger.LogInformation("🌐 Authorization URL: {Url}", requestTokenResponse.AuthorizationUrl);

                // Open browser
                try
                {
                    var processInfo = new ProcessStartInfo
                    {
                        FileName = requestTokenResponse.AuthorizationUrl,
                        UseShellExecute = true
                    };
                    Process.Start(processInfo);
                    _logger.LogInformation("✅ Browser opened successfully");
                }
                catch (Exception browserEx)
                {
                    _logger.LogWarning(browserEx, "⚠️ Could not open browser automatically");
                    throw new InvalidOperationException($"Could not open browser. Please manually navigate to: {requestTokenResponse.AuthorizationUrl}");
                }

                // Wait for callback
                _logger.LogInformation("⏳ Waiting for OAuth callback...");
                var callbackResult = await callbackTask;

                if (!callbackResult.Success)
                {
                    throw new InvalidOperationException($"OAuth callback failed: {callbackResult.Error}");
                }

                _logger.LogInformation("✅ OAuth callback received successfully!");

                // Complete authentication with the received verifier
                var accessTokenResponse = await CompleteAuthenticationAsync(
                    callbackResult.OAuthVerifier!,
                    requestTokenResponse.Token,
                    requestTokenResponse.TokenSecret,
                    cancellationToken);

                _logger.LogInformation("🎉 OAuth callback authentication completed successfully!");
                return accessTokenResponse;
            }
            finally
            {
                // Restore original callback URL
                _options.CallbackUrl = originalCallbackUrl;
            }
        }
        finally
        {
            callbackServer?.Dispose();
        }
    }

    public async Task<bool> LoadStoredCredentialsAsync()
    {
        try
        {
            _logger.LogDebug("Attempting to load stored credentials...");
            var storedCredentials = await _credentialStorage.RetrieveCredentialsAsync();
            if (storedCredentials != null)
            {
                _logger.LogDebug("Found stored credentials for user: {UserNickname}", storedCredentials.UserNickname);
                _logger.LogDebug("Stored access token: {Token}...", storedCredentials.AccessToken?.Substring(0, 8));
                _logger.LogDebug("Stored access token secret: {Secret}...", storedCredentials.AccessTokenSecret?.Substring(0, 8));

                _credentials.AccessToken = storedCredentials.AccessToken;
                _credentials.AccessTokenSecret = storedCredentials.AccessTokenSecret;

                _logger.LogDebug("Credentials loaded into memory. IsAuthenticated: {IsAuthenticated}", _credentials.IsAuthenticated);
                _logger.LogInformation("Stored credentials loaded successfully for user: {UserNickname}", storedCredentials.UserNickname);
                AuthenticationStatusChanged?.Invoke(this, new AuthenticationStatusChangedEventArgs(true, storedCredentials.UserNickname));
                return true;
            }

            _logger.LogDebug("No stored credentials found");
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load stored credentials");
            return false;
        }
    }

    public async Task StoreCredentialsAsync()
    {
        if (!IsAuthenticated)
        {
            throw new InvalidOperationException("Cannot store credentials when not authenticated");
        }

        await _credentialStorage.StoreCredentialsAsync(_credentials.AccessToken!, _credentials.AccessTokenSecret!);
    }

    public async Task ClearStoredCredentialsAsync()
    {
        await _credentialStorage.ClearCredentialsAsync();
        _credentials.AccessToken = null;
        _credentials.AccessTokenSecret = null;

        _logger.LogInformation("Credentials cleared");
        AuthenticationStatusChanged?.Invoke(this, new AuthenticationStatusChangedEventArgs(false));
    }

    public HttpRequestMessage CreateAuthenticatedRequest(
        HttpMethod httpMethod,
        string url,
        Dictionary<string, string>? parameters = null)
    {
        _logger.LogDebug("CreateAuthenticatedRequest called for {Method} {Url}", httpMethod.Method, url);
        _logger.LogDebug("Current authentication status: {IsAuthenticated}", IsAuthenticated);
        _logger.LogDebug("Is session-based authentication: {IsSessionBased}", IsSessionBasedAuthentication);
        _logger.LogDebug("AccessToken exists: {HasToken}", !string.IsNullOrEmpty(_credentials.AccessToken));
        _logger.LogDebug("AccessTokenSecret exists: {HasSecret}", !string.IsNullOrEmpty(_credentials.AccessTokenSecret));

        if (!IsAuthenticated)
        {
            _logger.LogError("Cannot create authenticated request - not authenticated");
            _logger.LogError("AccessToken: {Token}", _credentials.AccessToken ?? "NULL");
            _logger.LogError("AccessTokenSecret: {Secret}", _credentials.AccessTokenSecret ?? "NULL");
            throw new InvalidOperationException("Cannot create authenticated request when not authenticated");
        }

        // Handle session-based authentication differently
        if (IsSessionBasedAuthentication)
        {
            _logger.LogDebug("Creating session-based request (no OAuth signature required)");
            return CreateSessionBasedRequest(httpMethod, url);
        }

        _logger.LogDebug("Creating OAuth-based authenticated request for {Method} {Url}", httpMethod.Method, url);
        _logger.LogDebug("Using access token: {Token}...", _credentials.AccessToken?.Substring(0, 8));
        _logger.LogDebug("Using access token secret: {Secret}...", _credentials.AccessTokenSecret?.Substring(0, 8));

        parameters ??= new Dictionary<string, string>();

        // CRITICAL FIX: Extract query parameters from URL for OAuth signature
        // According to OAuth 1.0a spec, query parameters must be included in signature calculation
        var uri = new Uri(url);
        var baseUrl = $"{uri.Scheme}://{uri.Host}{uri.AbsolutePath}";

        if (!string.IsNullOrEmpty(uri.Query))
        {
            _logger.LogDebug("🔍 Extracting query parameters from URL for OAuth signature: {Query}", uri.Query);
            var queryParams = ParseQueryString(uri.Query.TrimStart('?'));
            foreach (var kvp in queryParams)
            {
                parameters[kvp.Key] = kvp.Value;
                _logger.LogDebug("  Added query param: {Key}={Value}", kvp.Key, kvp.Value);
            }
        }

        var oauthParameters = new Dictionary<string, string>
        {
            ["oauth_consumer_key"] = _credentials.ConsumerKey,
            ["oauth_nonce"] = OAuthSignatureGenerator.GenerateNonce(),
            ["oauth_signature_method"] = "HMAC-SHA1",
            ["oauth_timestamp"] = OAuthSignatureGenerator.GenerateTimestamp(),
            ["oauth_token"] = _credentials.AccessToken!,
            ["oauth_version"] = "1.0"
        };

        _logger.LogDebug("OAuth parameters: {Parameters}", string.Join(", ", oauthParameters.Select(kvp => $"{kvp.Key}={kvp.Value}")));

        // Combine OAuth parameters with request parameters for signature generation
        var allParameters = new Dictionary<string, string>(parameters);
        foreach (var kvp in oauthParameters)
        {
            allParameters[kvp.Key] = kvp.Value;
        }

        _logger.LogDebug("Generating OAuth signature for {Method} {BaseUrl}", httpMethod.Method, baseUrl);
        _logger.LogDebug("All parameters for signature: {Parameters}", string.Join(", ", allParameters.Select(kvp => $"{kvp.Key}={kvp.Value}")));

        // Use the fixed OAuth signature generator with base URL (no query params) and all parameters
        var signature = OAuthSignatureGeneratorFixed.GenerateSignature(
            httpMethod.Method, baseUrl, allParameters, _credentials.ConsumerSecret, _credentials.AccessTokenSecret);
        oauthParameters["oauth_signature"] = signature;

        _logger.LogDebug("Generated signature: {Signature}...", signature.Substring(0, Math.Min(8, signature.Length)));

        var authHeader = CreateAuthorizationHeader(oauthParameters);

        // Validate URL before creating the request
        if (string.IsNullOrWhiteSpace(url))
        {
            _logger.LogError("Cannot create authenticated request: URL is null or empty");
            throw new ArgumentException("URL cannot be null or empty", nameof(url));
        }

        if (!Uri.IsWellFormedUriString(url, UriKind.Absolute))
        {
            _logger.LogError("Cannot create authenticated request: URL is malformed: '{Url}'", url);
            throw new UriFormatException($"The URL '{url}' is not a valid absolute URI");
        }

        try
        {
            var request = new HttpRequestMessage(httpMethod, url);
            request.Headers.Add("Authorization", authHeader);

            _logger.LogDebug("Full Authorization header: {Header}", authHeader);
            _logger.LogDebug("OAuth parameters used: {Parameters}",
                string.Join(", ", oauthParameters.Select(kvp => $"{kvp.Key}={kvp.Value.Substring(0, Math.Min(10, kvp.Value.Length))}...")));
            _logger.LogDebug("Successfully created authenticated request");

            return request;
        }
        catch (UriFormatException ex)
        {
            _logger.LogError(ex, "Failed to create HttpRequestMessage with URL: '{Url}'", url);
            throw new UriFormatException($"Invalid URL format: '{url}'. {ex.Message}", ex);
        }
    }

    /// <summary>
    /// Gets the user nickname from the SmugMug API using authenticated request
    /// </summary>
    private async Task<string> GetUserNicknameFromApiAsync(CancellationToken cancellationToken = default)
    {
        var url = "https://api.smugmug.com/api/v2!authuser";

        var oauthParameters = new Dictionary<string, string>
        {
            ["oauth_consumer_key"] = _credentials.ConsumerKey,
            ["oauth_nonce"] = OAuthSignatureGeneratorFixed.GenerateNonce(),
            ["oauth_signature_method"] = "HMAC-SHA1",
            ["oauth_timestamp"] = OAuthSignatureGeneratorFixed.GenerateTimestamp(),
            ["oauth_token"] = _credentials.AccessToken!,
            ["oauth_version"] = "1.0"
        };

        _logger.LogInformation("🔐 Generated OAuth parameters:");
        _logger.LogInformation("  oauth_consumer_key: {ConsumerKey}", oauthParameters["oauth_consumer_key"]);
        _logger.LogInformation("  oauth_nonce: {Nonce}", oauthParameters["oauth_nonce"]);
        _logger.LogInformation("  oauth_signature_method: {SignatureMethod}", oauthParameters["oauth_signature_method"]);
        _logger.LogInformation("  oauth_timestamp: {Timestamp}", oauthParameters["oauth_timestamp"]);
        _logger.LogInformation("  oauth_token: {Token}", oauthParameters["oauth_token"]);
        _logger.LogInformation("  oauth_version: {Version}", oauthParameters["oauth_version"]);

        var signature = OAuthSignatureGeneratorFixed.GenerateSignature(
            "GET", url, oauthParameters, _credentials.ConsumerSecret, _credentials.AccessTokenSecret);
        oauthParameters["oauth_signature"] = signature;

        var authHeader = CreateAuthorizationHeader(oauthParameters);
        var request = new HttpRequestMessage(HttpMethod.Get, url);
        request.Headers.Add("Authorization", authHeader);
        request.Headers.Add("Accept", "application/json");

        var response = await _httpClient.SendAsync(request, cancellationToken);
        var content = await response.Content.ReadAsStringAsync(cancellationToken);

        _logger.LogInformation("📥 Response Status: {StatusCode}", response.StatusCode);
        _logger.LogInformation("📋 Full API Response: {Content}", content);

        if (response.IsSuccessStatusCode)
        {
            using var jsonDoc = JsonDocument.Parse(content);
            var root = jsonDoc.RootElement;

            if (root.TryGetProperty("Response", out var responseElement) &&
                responseElement.TryGetProperty("User", out var user) &&
                user.TryGetProperty("NickName", out var nickname))
            {
                return nickname.GetString() ?? "SmugMugUser";
            }
        }

        return "SmugMugUser";
    }

    /// <summary>
    /// Gets detailed user information after successful authentication to verify access level and permissions
    /// </summary>
    public async Task GetDetailedUserInformationAsync(CancellationToken cancellationToken = default)
    {
        if (!IsAuthenticated)
        {
            _logger.LogWarning("Cannot get user information - not authenticated");
            return;
        }

        try
        {
            _logger.LogInformation("🔍 Getting detailed user information from SmugMug API...");
            var request = CreateAuthenticatedRequest(HttpMethod.Get, "https://api.smugmug.com/api/v2!authuser");
            request.Headers.Add("Accept", "application/json");

            var response = await _httpClient.SendAsync(request, cancellationToken);

            _logger.LogDebug("User info API response: {StatusCode} {ReasonPhrase}",
                response.StatusCode, response.ReasonPhrase);

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                _logger.LogInformation("✅ Successfully retrieved user information from SmugMug API");
                _logger.LogInformation("📋 DETAILED USER INFORMATION:");
                _logger.LogInformation("Raw API Response: {Content}", content);

                // Try to parse and extract key information
                try
                {
                    using var jsonDoc = JsonDocument.Parse(content);
                    var root = jsonDoc.RootElement;

                    if (root.TryGetProperty("Response", out var response_element))
                    {
                        if (response_element.TryGetProperty("User", out var user))
                        {
                            _logger.LogInformation("🎯 USER DETAILS:");

                            if (user.TryGetProperty("NickName", out var nickname))
                                _logger.LogInformation("  Nickname: {Nickname}", nickname.GetString());

                            if (user.TryGetProperty("Name", out var name))
                                _logger.LogInformation("  Name: {Name}", name.GetString());

                            if (user.TryGetProperty("Uri", out var uri))
                                _logger.LogInformation("  User URI: {Uri}", uri.GetString());

                            if (user.TryGetProperty("Uris", out var uris))
                            {
                                _logger.LogInformation("🔗 AVAILABLE ENDPOINTS:");
                                if (uris.TryGetProperty("Node", out var nodeUri))
                                    _logger.LogInformation("  Node URI: {NodeUri}", nodeUri.TryGetProperty("Uri", out var nodeUriValue) ? nodeUriValue.GetString() : "Not available");

                                if (uris.TryGetProperty("Folder", out var folderUri))
                                    _logger.LogInformation("  Folder URI: {FolderUri}", folderUri.TryGetProperty("Uri", out var folderUriValue) ? folderUriValue.GetString() : "Not available");

                                if (uris.TryGetProperty("UserProfile", out var profileUri))
                                    _logger.LogInformation("  Profile URI: {ProfileUri}", profileUri.TryGetProperty("Uri", out var profileUriValue) ? profileUriValue.GetString() : "Not available");
                            }
                        }
                    }

                    // Check for access level indicators
                    _logger.LogInformation("🔐 ACCESS LEVEL ANALYSIS:");
                    if (content.Contains("Node") && content.Contains("Folder"))
                    {
                        _logger.LogInformation("✅ FULL ACCESS CONFIRMED - Node and Folder endpoints available");
                        _logger.LogInformation("✅ Can access folder structure and private content");
                    }
                    else
                    {
                        _logger.LogWarning("⚠️  LIMITED ACCESS - Missing Node or Folder endpoints");
                        _logger.LogWarning("⚠️  May not be able to access full folder structure");
                    }
                }
                catch (JsonException ex)
                {
                    _logger.LogWarning(ex, "Could not parse user information JSON, but API call was successful");
                }
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);
                _logger.LogError("❌ Failed to get user information: {StatusCode} - {Content}",
                    response.StatusCode, errorContent);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Exception while getting user information");
        }
    }

    /// <summary>
    /// Tests the current credentials by making a simple API call
    /// </summary>
    public async Task<bool> TestCredentialsAsync(CancellationToken cancellationToken = default)
    {
        if (!IsAuthenticated)
        {
            _logger.LogWarning("Cannot test credentials - not authenticated");
            return false;
        }

        try
        {
            _logger.LogDebug("Testing credentials with simple API call...");
            var request = CreateAuthenticatedRequest(HttpMethod.Get, "https://api.smugmug.com/api/v2!authuser");
            request.Headers.Add("Accept", "application/json");

            var response = await _httpClient.SendAsync(request, cancellationToken);

            _logger.LogDebug("Credential test response: {StatusCode} {ReasonPhrase}",
                response.StatusCode, response.ReasonPhrase);

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                _logger.LogDebug("Credential test successful. Response length: {Length}", content.Length);
                return true;
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);
                _logger.LogWarning("Credential test failed: {StatusCode} - {Content}",
                    response.StatusCode, errorContent);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception during credential test");
            return false;
        }
    }

    private static string CreateAuthorizationHeader(Dictionary<string, string> parameters)
    {
        var headerParams = parameters
            .Where(kvp => kvp.Key.StartsWith("oauth_"))
            .OrderBy(kvp => kvp.Key)
            .Select(kvp => $"{OAuthSignatureGeneratorFixed.PercentEncode(kvp.Key)}=\"{OAuthSignatureGeneratorFixed.PercentEncode(kvp.Value)}\"");

        return $"OAuth {string.Join(", ", headerParams)}";
    }

    /// <summary>
    /// Creates a session-based request that doesn't require OAuth signatures
    /// This is used when the user is authenticated via browser session
    /// </summary>
    private HttpRequestMessage CreateSessionBasedRequest(HttpMethod httpMethod, string url)
    {
        try
        {
            var request = new HttpRequestMessage(httpMethod, url);

            // Add headers that mimic browser requests to maintain session compatibility
            request.Headers.Add("Accept", "application/json");
            request.Headers.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
            request.Headers.Add("Referer", "https://secure.smugmug.com/");

            _logger.LogDebug("Created session-based request for {Method} {Url}", httpMethod.Method, url);
            _logger.LogDebug("Session-based request headers: Accept, User-Agent, Referer");

            return request;
        }
        catch (UriFormatException ex)
        {
            _logger.LogError(ex, "Failed to create session-based HttpRequestMessage with URL: '{Url}'", url);
            throw new UriFormatException($"Invalid URL format: '{url}'. {ex.Message}", ex);
        }
    }

    private static Dictionary<string, string> ParseQueryString(string queryString)
    {
        var result = new Dictionary<string, string>();
        var pairs = queryString.Split('&');

        foreach (var pair in pairs)
        {
            var keyValue = pair.Split('=', 2);
            if (keyValue.Length == 2)
            {
                result[Uri.UnescapeDataString(keyValue[0])] = Uri.UnescapeDataString(keyValue[1]);
            }
        }

        return result;
    }
}
