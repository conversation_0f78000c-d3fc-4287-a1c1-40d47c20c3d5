# SmugMug API Node ID Extraction Fix

## Problem Identified

The user pointed out that `_expand=Node` may not be the right approach for getting folder structure, and suggested using the Node ID directly from the `/api/v2!authuser` response.

## Analysis of the Response Structure

From the user's provided JSON response, we can see that the `/api/v2!authuser` endpoint returns:

```json
{
    "Response": {
        "User": {
            "NickName": "yuvin",
            "Name": "<PERSON>vin <PERSON>",
            "ImageCount": 69604,
            "Uris": {
                "Node": {
                    "Uri": "/api/v2/node/KssX2d"
                },
                "Folder": {
                    "Uri": "/api/v2/folder/user/yuvin"
                },
                "UserAlbums": {
                    "Uri": "/api/v2/user/yuvin!albums"
                }
            }
        }
    }
}
```

## Key Issues Fixed

### 1. **Incorrect Response Model Structure**
- **Problem**: Our `SmugMugApiResponse<SmugMugUser>` expected user data directly under `Response`, but the actual structure nests it under `Response.User`
- **Solution**: Created `SmugMugAuthUserResponse` model to handle the nested structure

### 2. **Unnecessary _expand Parameter**
- **Problem**: Using `_expand=Node` was causing issues and is not needed
- **Solution**: Removed `_expand=Node` parameter from `/api/v2!authuser` calls

### 3. **Node ID Extraction**
- **Problem**: Not properly extracting Node ID from the Node URI
- **Solution**: Added `NodeId` property to `SmugMugUser` that extracts ID from URI like `/api/v2/node/KssX2d` → `KssX2d`

### 4. **Improved Child Node Retrieval**
- **Problem**: Using `_expand=ChildNodes` was causing issues
- **Solution**: Use direct `/api/v2/node/{nodeId}!children` endpoint without expansion

## Changes Made

### 1. Updated SmugMugUser Model
```csharp
// Added comprehensive user properties
[JsonPropertyName("FirstName")]
public string? FirstName { get; set; }

[JsonPropertyName("LastName")]
public string? LastName { get; set; }

[JsonPropertyName("Email")]
public string? Email { get; set; }

[JsonPropertyName("Plan")]
public string? Plan { get; set; }

[JsonPropertyName("AccountStatus")]
public string? AccountStatus { get; set; }

[JsonPropertyName("ImageCount")]
public int? ImageCount { get; set; }

// Added Node ID extraction helper
public string? NodeId
{
    get
    {
        if (Uris?.Node?.Uri != null)
        {
            var parts = Uris.Node.Uri.Split('/');
            return parts.Length > 0 ? parts[^1] : null;
        }
        return null;
    }
}
```

### 2. Created SmugMugAuthUserResponse Model
```csharp
public class SmugMugAuthUserResponse
{
    [JsonPropertyName("User")]
    public SmugMugUser? User { get; set; }
    
    // Additional response metadata...
}
```

### 3. Updated SmugMugUris Model
```csharp
// Added all important URIs from the response
[JsonPropertyName("Folder")]
public SmugMugUriInfo? Folder { get; set; }

[JsonPropertyName("UserAlbums")]
public SmugMugUriInfo? UserAlbums { get; set; }

[JsonPropertyName("UserRecentImages")]
public SmugMugUriInfo? UserRecentImages { get; set; }
// ... and more
```

### 4. Updated API Client Methods

#### GetAuthenticatedUserAsync
- Changed endpoint from `/api/v2/user/!authuser?_expand=Node` to `/api/v2!authuser`
- Updated to use `SmugMugApiResponse<SmugMugAuthUserResponse>`
- Enhanced logging to show all available URIs and extracted Node ID

#### GetUserRootNodeAsync
- Now uses Node ID directly from authuser response
- Calls `GetNodeAsync(user.NodeId)` instead of relying on expansion
- Improved fallback mechanisms

#### GetChildNodesAsync
- Removed `_expand=ChildNodes` parameter
- Uses direct `/api/v2/node/{nodeId}!children` endpoint

## Proper API Call Sequence

Based on the authuser response, the correct sequence is:

1. **Get User Info**: `GET /api/v2!authuser`
   - Extract Node ID: `KssX2d`
   - Extract Folder URI: `/api/v2/folder/user/yuvin`
   - Extract UserAlbums URI: `/api/v2/user/yuvin!albums`

2. **Get Root Node**: `GET /api/v2/node/KssX2d`
   - Get root folder details

3. **Get Folder Structure**: `GET /api/v2/node/KssX2d!children`
   - Get immediate children of root folder

4. **Alternative Approaches**:
   - Legacy Folder API: `GET /api/v2/folder/user/yuvin`
   - All Albums: `GET /api/v2/user/yuvin!albums`

## Testing Results

Created a test program that successfully:
- ✅ Parsed the nested JSON structure
- ✅ Extracted Node ID `KssX2d` from URI `/api/v2/node/KssX2d`
- ✅ Captured all user information (Name, Email, Plan, ImageCount, etc.)
- ✅ Identified all available URIs for further API calls

## Next Steps

The updated implementation should now:
1. Properly extract Node ID from authuser response
2. Use the Node ID directly for folder structure calls
3. Avoid problematic `_expand` parameters
4. Provide comprehensive logging of available URIs and user information

This approach follows the SmugMug API best practices and uses the actual response structure rather than making assumptions about expansions.
